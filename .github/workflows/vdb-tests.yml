name: Run VDB Tests

on:
  pull_request:
    branches:
      - main
    paths:
      - api/core/rag/datasource/**
      - docker/**
      - .github/workflows/vdb-tests.yml
      - api/poetry.lock
      - api/pyproject.toml

concurrency:
  group: vdb-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    name: VDB Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.11"
          - "3.12"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Setup Poetry and Python ${{ matrix.python-version }}
        uses: ./.github/actions/setup-poetry
        with:
          python-version: ${{ matrix.python-version }}
          poetry-lockfile: api/poetry.lock

      - name: Check Poetry lockfile
        run: |
          poetry check -C api --lock
          poetry show -C api

      - name: Install dependencies
        run: poetry install -C api --with dev

      - name: Set up dotenvs
        run: |
          cp docker/.env.example docker/.env
          cp docker/middleware.env.example docker/middleware.env

      - name: Expose Service Ports
        run: sh .github/workflows/expose_service_ports.sh

      - name: Set up Vector Store (TiDB)
        uses: hoverkraft-tech/compose-action@v2.0.2
        with:
          compose-file: docker/tidb/docker-compose.yaml
          services: |
            tidb
            tiflash

      - name: Set up Vector Stores (Weaviate, Qdrant, PGVector, Milvus, PgVecto-RS, Chroma, MyScale, ElasticSearch, Couchbase)
        uses: hoverkraft-tech/compose-action@v2.0.2
        with:
          compose-file: |
            docker/docker-compose.yaml
          services: |
            weaviate
            qdrant
            couchbase-server
            etcd
            minio
            milvus-standalone
            pgvecto-rs
            pgvector
            opengauss
            chroma
            elasticsearch

      - name: Check TiDB Ready
        run: poetry run -P api python api/tests/integration_tests/vdb/tidb_vector/check_tiflash_ready.py

      - name: Test Vector Stores
        run: poetry run -P api bash dev/pytest/pytest_vdb.sh
