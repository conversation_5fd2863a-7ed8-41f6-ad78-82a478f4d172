"""短信服务模块"""
import json
import random
import string
import traceback
from typing import <PERSON>ple

from alibabacloud_dysmsapi20170525.client import Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from flask import current_app

from configs import sms_config
from extensions.ext_redis import redis_client


class SmsService:
    """短信服务类"""

    def __init__(self):
        """初始化阿里云短信客户端"""
        config = open_api_models.Config(
            access_key_id=sms_config.ACCESS_KEY_ID,
            access_key_secret=sms_config.ACCESS_KEY_SECRET,
            region_id=sms_config.REGION_ID
        )
        self.client = Client(config)

    def _generate_verification_code(self) -> str:
        """生成验证码
        
        Returns:
            str: 生成的验证码
        """
        return ''.join(random.choices(string.digits, k=sms_config.VERIFICATION_CODE_LENGTH))

    def _get_redis_key(self, phone: str) -> str:
        """获取验证码在Redis中的key
        
        Args:
            phone: 手机号
            
        Returns:
            str: Redis key
        """
        return f"{sms_config.VERIFICATION_CODE_PREFIX}{phone}"

    def _store_verification_code(self, phone: str, code: str) -> None:
        """将验证码存储到Redis
        
        Args:
            phone: 手机号
            code: 验证码
        """
        redis_key = self._get_redis_key(phone)
        redis_client.setex(
            redis_key,
            sms_config.VERIFICATION_CODE_EXPIRE,
            code
        )

    def send_verification_code(self, phone: str) -> Tuple[bool, str]:
        """发送验证码短信
        
        Args:
            phone: 手机号
            
        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        try:
            # 生成验证码
            code = self._generate_verification_code()
            
            # 构建请求
            send_request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone,
                sign_name=sms_config.SIGN_NAME,
                template_code=sms_config.TEMPLATE_CODE,
                template_param=str({"code": code})
            )

            current_app.logger.info(f"send verification code reqest: {str(send_request)}")

            # 发送短信
            response = self.client.send_sms(send_request)
            current_app.logger.info(f"send verification code response: {str(response)}")

            if response.body.code == "OK":
                # 发送成功，存储验证码
                self._store_verification_code(phone, code)
                return True, ""
            else:
                return False, response.body.message
                
        except Exception as e:
            current_app.logger.error(f"Failed to send SMS: {str(e)}")
            return False, str(e)

    def verify_code(self, phone: str, code: str) -> bool:
        """验证短信验证码
        
        Args:
            phone: 手机号
            code: 待验证的验证码
            
        Returns:
            bool: 验证是否通过
        """
        try:
            redis_key = self._get_redis_key(phone)
            stored_code = redis_client.get(redis_key)
            
            if not stored_code:
                return False
                
            # 验证码匹配检查
            is_valid = stored_code.decode('utf-8') == code
                
            return is_valid
            
        except Exception as e:
            current_app.logger.error(f"Failed to verify code: {str(e)}")
            return False

    def send_notification_sms(self, phone: str, code: str) -> bool:
        """Send notification SMS"""
        # Check rate limit
        # if not self._check_rate_limit(phone):
        #     raise Exception("Too many SMS requests")

        # Prepare SMS DTO
        # sms_dto = SmsMessageDTO(
        #     targets=[phone],
        #     messageContent=content
        # )

        # 构建请求
        send_request = dysmsapi_models.SendSmsRequest(
            phone_numbers=phone,
            sign_name=sms_config.SIGN_NAME,
            template_code=sms_config.NOTIFICATION_TEMPLATE_CODE,
            template_param=str({"code": code})
        )

        # Send SMS
        try:
            # 发送短信
            response = self.client.send_sms(send_request)
            current_app.logger.info(f"send notification code response: {str(response)}")

            if response.body.code == "OK":
                return True, ""
            else:
                return False, response.body.message
        except Exception as e:
            current_app.logger.error(f"发送验证码失败:{traceback.format_exc()}")
            raise Exception(str(e))