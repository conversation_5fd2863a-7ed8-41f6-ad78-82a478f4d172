import logging
from typing import List, Dict, Generator

from openai import OpenAI

from configs.remote_settings_sources.apollo import apollo_client
from models.toc_user import ToCUser
import yw.llm.YwLiteLlmClient as YwLiteLlmClient

# 配置日志记录器
logger = logging.getLogger(__name__)

# 在此替换为您的实际API Key与Base URL
client = OpenAI(
    api_key="<your api key>",  # 替换为实际的api_key
    base_url="http://124.223.22.249:6399/v1"  # 替换为实际的base_url
)

QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_API_KEY = apollo_client.get_value("QWEN_API_KEY")
QWEN_MODEL_NAME = "qwen3-235b-a22b"

qwenClient = OpenAI(
    api_key=QWEN_API_KEY,
    base_url=QWEN_API_URL
)

# 用于缓存 YwLiteLlmClient 实例的字典
_llm_clients_cache: Dict[str, YwLiteLlmClient] = {}

def _get_llm_client(user_id: str, conversation_id: str) -> YwLiteLlmClient:
    """
    根据 conversation_id 获取或创建 YwLiteLlmClient 实例。
    如果缓存中存在，则直接复用；否则，通过 get_cache_llm 获取并缓存。
    """
    if conversation_id not in _llm_clients_cache:
        # 如果缓存中没有，则通过 get_cache_llm 获取并存入缓存
        llm_instance = YwLiteLlmClient.get_cache_llm(
            "plan",
            "plan",
            user=user_id,
            session_id=conversation_id
        )
        _llm_clients_cache[conversation_id] = llm_instance
    return _llm_clients_cache[conversation_id]


def release_llm_client(conversation_id: str):
    """
    根据 conversation_id 释放并移除对应的 YwLiteLlmClient 实例。
    """
    if conversation_id in _llm_clients_cache:
        _llm_clients_cache.pop(conversation_id)
        print(f"LLM client for conversation_id '{conversation_id}' has been released.")
    else:
        print(f"No LLM client found for conversation_id '{conversation_id}' in cache.")

###########################################
#  大模型调用函数
###########################################
def call_deepseek(prompt: str, system_prompt: str = None,
                  messages: List[Dict[str, str]] = None, stream: bool = False,
                  toc_user: ToCUser = None,
                  conversation_id: str = None) -> Generator[str, None, None]:
    """
    使用deepseek-v3模型，进行流式对话式Completion调用。
    返回一个生成器，逐次产生模型的输出内容。

    Args:
        prompt: 用户输入的提示词
        system_prompt: 系统提示词，可选

    Yields:
        str: 模型的输出内容片段
    """

    # 构造对话消息
    if not system_prompt:
        system_prompt = "You are an AI assistant designed to provide helpful, accurate, and concise responses to user inquiries. Follow instructions carefully and maintain a professional and polite tone."
    if not messages:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

    # 会话ID 可以对同一回话进行 归类

    llm = _get_llm_client(user_id=toc_user.id, conversation_id=conversation_id)

    temperature = apollo_client.get_value("PLAN_LLM_TEMPERATURE") if apollo_client.get_value("PLAN_LLM_TEMPERATURE") else 0.1
    # 启用流式模式并处理分块响应
    response_stream = llm.invoke(
        messages,
        stream=True,  # 关键参数：启用流式输出
        temperature = temperature
    )
    # 逐块处理响应

    full_response = ""
    for chunk in response_stream:
        content = None
        if isinstance(chunk, tuple) and len(chunk) > 1 and chunk[0] == 'content':
            content = chunk[1]  # 元组形式
        elif hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(
                chunk.choices[0].delta, 'content'):
            content = chunk.choices[0].delta.content  # 兼容旧结构

        if content is not None:
            full_response += content
            if stream:
                yield content

    if not stream:
        yield full_response

def call_qwen(prompt: str, system_prompt: str = None,
              messages: List[Dict[str, str]] = None, stream: bool = False) -> Generator[str, None, None]:
    """
    使用qwen模型，进行流式对话式Completion调用。
    返回一个生成器，逐次产生模型的输出内容。

    Args:
        prompt: 用户输入的提示词
        system_prompt: 系统提示词，可选

    Yields:
        str: 模型的输出内容片段
    """
    # 构造对话消息
    if not system_prompt:
        system_prompt = "You are an AI assistant designed to provide helpful, accurate, and concise responses to user inquiries. Follow instructions carefully and maintain a professional and polite tone."
    if not messages:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

    # 发起流式模型推理请求
    response = qwenClient.chat.completions.create(
        model=QWEN_MODEL_NAME,
        messages=messages,
        stream=True  # 启用流式输出
    )

    # 逐次产生模型的输出内容
    full_response = ""
    for chunk in response:
        if chunk.choices[0].delta.content is not None:
            full_response += chunk.choices[0].delta.content
            if stream:
                yield chunk.choices[0].delta.content
    if not stream:
        yield full_response
