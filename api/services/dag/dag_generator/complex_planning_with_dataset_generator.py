import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tu<PERSON>, Generator, Mapping, Set

from models import db, Dataset
from models.toc_user import ToCUser
from services.dataset_service import DatasetService
from .complex_planning_base_generator import (ComplexPlanningBaseGenerator, TaskNode, TaskStatus, extract_inputs,
                                              generate_task_id, call_llm_analyze_parameters,
                                              generate_schedule_for_task)
from .util import McpTools, KnowledgeBaseAnalyzer, FileContentExtractor, llm_client
from .task_validator import TaskValidator

logger = logging.getLogger(__name__)

class ComplexPlanningWithDatasetGenerator(ComplexPlanningBaseGenerator):

    def __init__(self, conversation_id: str):
        super().__init__(conversation_id)
        self._retry_counters = {}  # 用于跟踪各个生成函数的重试次数

    def _get_retry_key(self, func_name: str, task_id: str) -> str:
        """生成重试计数器的键"""
        return f"{func_name}_{task_id}"

    def _increment_retry_counter(self, func_name: str, task_id: str) -> int:
        """增加重试计数并返回当前重试次数"""
        key = self._get_retry_key(func_name, task_id)
        self._retry_counters[key] = self._retry_counters.get(key, 0) + 1
        return self._retry_counters[key]

    def map_task_status(self, chinese_status: str) -> TaskStatus:
        """Map Chinese task status to English TaskStatus enum"""
        status_map = {
            "待规划": TaskStatus.PENDING,
            "已规划": TaskStatus.PLANNED,
            "执行中": TaskStatus.IN_PROGRESS,
            "已完成": TaskStatus.COMPLETED,
            "失败": TaskStatus.FAILED
        }
        return status_map.get(chinese_status, TaskStatus.PENDING)

    def call_llm_generate_subtasks(
            self,
            root_task: TaskNode,
            current_task: TaskNode,
            task_context: Optional[List[Dict[str, Any]]] = None,
            retrieve_datas: Optional[Dict[str, List[str]]] = None,
            history_messages: Optional[List[Dict[str, str]]] = [],
            toc_user: Optional[ToCUser] = None,
            user_location_info: dict = None
    ) -> Generator[str, None, List[TaskNode]]:
        """
        修改提示词，让大模型一次性生成所有子任务及其依赖关系，支持流式输出
        Args:
            retrieve_datas: 检索到的数据字典，key为数据源名，value为内容列表
        """
        if retrieve_datas is None:
            retrieve_datas = {}
        if history_messages is None:
            history_messages = []
        context_desc = ""
        if task_context:
            context_desc = "\n任务上下文（从根节点到当前节点）：\n"
            for task in task_context:
                context_desc += f"- 任务ID: {task['task_id']}\n"
                context_desc += f"  名称: {task['task_name']}\n"
                context_desc += f"  描述: {task['task_description']}\n"
                context_desc += f"  预期输出: {task['expected_deliverables']}\n"
                context_desc += "\n"
        reference_information = ""
        if retrieve_datas:
            for source_name, items in retrieve_datas.items():
                reference_information += f"{source_name}:\n"
                if isinstance(items, list):
                    for item in items:
                        reference_information += f"{item}\n"
                else:
                    reference_information += f"{items}"

        available_tools = McpTools.retrieve_tools_for_task(current_task.task_description)
        logger.info(f"[PLAN_GEN] 可用工具列表: {json.dumps(available_tools, ensure_ascii=False, indent=2)}")

        system_prompt = """You are a top-tier task decomposition expert, well-versed in project management and workflow optimization.

**Core Mission:**
Your core mission is to accurately decompose complex tasks provided by the user into their **[direct, actionable]** subtasks. You must always adhere to best practices in project management and workflow optimization, maintaining utmost fidelity to the task's hierarchical structure.

**Inviolable Core Principles:**

1.  **Primacy of Hierarchical Structure:**
    *   Before decomposing any task, you must first thoroughly analyze and understand the complete task hierarchy, based on reference information (if provided).
    *   Accurately identify the current task's position within the overall hierarchy.

2.  **Immutable Law of Subtask Extraction:**
    *   Your output **[must only]** contain the **[direct subtasks]** of the current task.
    *   It is strictly forbidden to include in the output: the parent task of the current task, any indirect subtasks (e.g., subtasks of subtasks, or any deeper-level tasks), or sibling tasks of the current task.

3.  **Clarity and Completeness of Task Definitions:**
    *   Ensure each subtask has clear task boundaries and appropriate parent-child relationships.
    *   Subtask descriptions must remain clear, concise, and actionable.

4.  **Output Format:**
    *   All responses **[must]** be provided in valid JSON format, without any additional text outside the JSON object.

**Internal Processing Guidance:**
Before identifying and extracting subtasks, prioritize a complete understanding of the overall task hierarchy and the positioning of the current task.
    """

        prompt = f"""
    Your objective is to decompose the main task provided below into a set of direct, actionable subtasks. Adhere strictly to the principles, execution steps, and output format specified.
    
    Task Decomposition Request:
    
    CURRENT TASK:
    Name: {current_task.task_name}
    Description: {current_task.task_description}
    
    TASK CONTEXT:
    {context_desc}
    
    USER_INFO:
    {json.dumps(user_location_info)} // Contains user location (e.g., city, country), current local time, and other relevant details
    
    REFERENCE INFORMATION:
    {reference_information}
    
    HISTORY USER MESSAGES (may be empty, just for reference):
    {[f"User: {msg['query']}{chr(10)}Assistant: {msg['response']}" for msg in history_messages]}
    
    CONTEXT ANALYSIS GUIDELINES:
    1. **Prioritize Reference Information**: Treat `REFERENCE INFORMATION` as the primary source for task structure and hierarchy. Strictly adhere to any defined task breakdowns, constraints, or guidelines provided in the reference information.
    2. **Understand Overall Context and Intent**: Analyze the conversation history to grasp the user’s overarching goals and implicit intent, but only to supplement reference information. If the task description is vague, infer the user’s ultimate objective based on history and context, ensuring alignment with reference information.
    3. **Leverage User Information**: Use `USER_INFO` (e.g., location, current time) to tailor subtasks, such as accounting for time zones, local resource availability, or location-specific constraints, while ensuring consistency with reference information.
    4. **Extract Key Information from History**: Identify specific constraints, preferences, or prior decisions from the conversation history to refine subtasks, but only apply these if they align with or do not contradict the reference information. Prioritize recurring themes or explicitly stated user needs that complement the reference.
    5. **Ensure Consistency**: Align subtasks with the reference information, user preferences, and prior task definitions or feedback to maintain coherence.
    6. **Personalize Subtasks**: Craft subtasks that reflect the user’s unique needs (e.g., preferred tools, timelines, or constraints) as inferred from history or user information, provided they do not conflict with the reference information.

## 1. Core Task Decomposition Principles:

* **Primary Goal:** Break down the Main Task into a list of its immediate, logical, and manageable subtasks.
* **Hierarchical Constraint (Critical):**
    * You MUST extract and return **ONLY the direct children (subtasks)** of the Main Task.
    * You MUST NOT include the Main Task itself in the subtask list.
    * You MUST NOT include any parent tasks, grandparent tasks, or any tasks higher in the hierarchy than the Main Task.
    * You MUST NOT include any indirect subtasks (e.g., grandchildren, great-grandchildren, or any deeper-level tasks).
    * You MUST NOT include any sibling tasks of the Main Task.
* **Scope and Boundaries:**
    * Each subtask must be unequivocally and solely within the scope of the Main Task.
    * Collectively, the identified subtasks should comprehensively cover all aspects of the Main Task.
* **Reference Information Adherence:** If reference information regarding task structure is provided, strictly follow the defined hierarchy.
* **Atomicity of Subtasks:**
    * Each subtask should be atomic, meaning it represents a single, well-defined piece of work that cannot be meaningfully broken down further for the current level of decomposition.
    * It must have clear, measurable, and distinct deliverables or outcomes.
* **Subtask Descriptions:**
    * `task_name`: Must be a concise and clear summary of the subtask.
    * `task_description`: Must be detailed, actionable, and self-contained. It should include all necessary input information or references for the subtask to be understood and executed. Clearly state the expected deliverables.
* **Dependencies:**
    * `data_dependencies`: List `task_id`s of other subtasks (at the same decomposition level) that must be completed before this subtask can start because it requires their output data.
    * `sibling_dependencies`: List `task_id`s of other subtasks (at the same decomposition level) that must be completed before this subtask can start due to logical sequencing, even if there's no direct data dependency.

## 2. Execution Steps:

1.  **Analyze Main Task & Hierarchy:**
    * Thoroughly read and understand the Main Task description.
    * If reference information on hierarchy is provided, analyze it to understand the task's context and potential child tasks.
2.  **Identify Direct Subtasks:**
    * Based on the Main Task, identify only its immediate, direct subtasks that fulfill the Core Task Decomposition Principles.
3.  **Formulate Subtask Details:**
    * For each identified direct subtask, define all fields required by the JSON output structure specified below (task_id, task_name, task_description, is_atomic, dependencies, expected_deliverables, etc.).
    * Ensure `task_id`s are unique for this decomposition set.
4.  **Construct JSON Output:**
    * Assemble the subtasks into a JSON array as per the "Output Format and Requirements."
5.  **Self-Verification (Crucial):**
    * **Hierarchy Check:** Confirm that ONLY direct subtasks of the Main Task are included. Verify no parent, grandparent, grandchild, deeper-level, or sibling tasks are present.
    * **Principle Alignment:** Ensure each subtask and its description align with all "Core Task Decomposition Principles" (atomicity, scope, clarity, etc.).
    * **Completeness Check:** Verify that the set of subtasks collectively covers the Main Task.
    * **Format Check:** Validate that the output is a valid JSON array adhering to the specified structure and that all fields are correctly populated.
    * **If any verification step fails, you MUST revise the subtasks and/or the JSON structure until all conditions are met before returning the result.**

## 3. Output Format and Requirements:

* **Format:** You MUST return a single, valid JSON array. The entire response must be enclosed in square brackets `[]`, with each subtask object separated by a comma and no trailing comma after the last object. Do not include any explanatory text, apologies, or any other content outside of this JSON array in your response.
* **Language:** All text content within the JSON (e.g., `task_name`, `task_description`, `expected_deliverables`) MUST be in **English**.
* **JSON Object Structure for Each Subtask:** Each element in the JSON array must be an object with the following structure and data types:

    ```json
    {{
        "task_id": "string", // Unique identifier for the subtask (e.g., "subtask_001")
        "task_name": "string", // Concise name of the subtask
        "task_description": "string", // Detailed description, including necessary inputs
        "task_status": "Pending", // Default status, should always be "Pending"
        "is_atomic": boolean, // True if the task cannot be meaningfully broken down further at this level
        "data_dependencies": ["array_of_string_task_ids"], // task_ids this subtask depends on for data
        "sibling_dependencies": ["array_of_string_task_ids"], // task_ids this subtask depends on for execution order (same level)
        "expected_deliverables": "string", // Clear description of what this subtask will produce
        "start_time": "string",  // Optional ISO timestamp eg: 2025-06-18T15:20:00+09:00
        "completion_time": "string"  // Optional ISO timestamp eg: 2025-06-18T15:20:00+09:00
    }}
    ```
    Please return only valid JSON without any additional text or explanations.
    Ensure your response is in Chinese.
    """

        logger.info("[PLAN_GEN] 开始调用大模型生成子任务 系统提示词:")
        logger.info(system_prompt)
        logger.info("[PLAN_GEN] 开始调用大模型生成子任务 用户提示词:")
        logger.info(prompt)
        # 收集所有流式输出
        full_response = ""
        root_task.log += f"正在拆解任务：{current_task.task_name}\n"
        yield root_task.to_dict()

        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"[PLAN_GEN] 开始调用大模型生成子任务 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

        for chunk in llm_client.call_deepseek(
            prompt=prompt,
            system_prompt=system_prompt,
            toc_user=toc_user,
            conversation_id=self.conversation_id
        ):
            full_response += chunk

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 大模型生成子任务完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"[PLAN_GEN] 生成PLAN耗时统计：大模型生成子任务耗时: {duration:.2f}秒")

        root_task.log += f"任务:{current_task.task_name} 拆解完成\n"
        yield root_task.to_dict()

        logger.info("[PLAN_GEN] 大模型返回结果:")
        logger.info(full_response)

        # 处理完整的响应
        try:
            # 清理可能存在的markdown代码块标记
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            subtasks_data = json.loads(full_response)
            logger.info("[PLAN_GEN] 解析后的子任务数据:")
            logger.info(json.dumps(subtasks_data, ensure_ascii=False, indent=2))

            # 确保 subtasks_data 是列表类型
            if not isinstance(subtasks_data, list):
                if isinstance(subtasks_data, dict):
                    subtasks_data = [subtasks_data]
                else:
                    raise Exception("大模型返回的数据格式错误，期望是任务列表")

            # 如果未拆解出来子任务则自动重试
            if not subtasks_data or len(subtasks_data) == 0:
                retry_count = self._increment_retry_counter("call_llm_generate_subtasks", current_task.task_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试生成子任务")
                    # 递归调用自身重新生成
                    yield from self.call_llm_generate_subtasks(root_task, current_task, task_context,
                                                               history_messages,
                                                               toc_user)
                    return current_task.children

            # 验证拆分结果
            root_task.log += f"正在验证任务拆分结果...\n"
            yield root_task.to_dict()

            # 调用大模型验证拆分结果
            validation_prompt = f"""
请验证以下任务拆分结果是否符合要求：

CURRENT TASK:
Name: {current_task.task_name}
Description: {current_task.task_description}

REFERENCE INFORMATION:
{reference_information}

GENERATED SUBTASKS:
{json.dumps(subtasks_data, ensure_ascii=False, indent=2)}

验证要求：
1. 所有子任务必须直接属于当前任务
2. 不能包含当前任务的父任务
3. 不能包含当前任务的兄弟任务
4. 不能包含更深层级的任务
5. 必须严格遵循参考信息中的任务层级结构

请返回一个JSON对象，格式如下：
{{
    "is_valid": boolean,  // 拆分结果是否有效
    "invalid_reasons": [  // 如果无效，列出原因
        "原因1",
        "原因2"
    ],
    "corrected_subtasks": [  // 如果无效，提供修正后的子任务列表
        {{
            "task_id": "unique_identifier",
            "task_name": "concise_description",
            "task_description": "detailed_description_with_inputs",
            "task_status": "Pending",
            "is_atomic": boolean,
            "data_dependencies": ["dependency_task_ids"],
            "sibling_dependencies": ["sibling_task_ids"],
            "expected_deliverables": "expected_outputs"
        }}
    ]
}}

如果拆分结果有效，corrected_subtasks 字段可以为空数组。
"""

            validation_system_prompt = """You are an expert task validation specialist. Your role is to:
1. Verify that task decomposition results strictly follow the hierarchical structure
2. Ensure only direct subtasks of the current task are included
3. Validate against reference information
4. Provide corrections if needed
5. Return responses in valid JSON format only

Key Responsibilities:
- Validate task hierarchy
- Check for proper parent-child relationships
- Ensure no deeper level tasks are included
- Verify against reference information
- Provide clear validation results
"""

            validation_response = ""
            for chunk in llm_client.call_deepseek(prompt=validation_prompt,
                                                  system_prompt=validation_system_prompt,
                                                  toc_user=toc_user,
                                                  conversation_id=self.conversation_id):
                validation_response += chunk

            validation_result = json.loads(validation_response.replace('```json', '').replace('```', '').strip())
            
            if not validation_result.get("is_valid", False):
                logger.warning(f"任务拆分验证失败: {validation_result.get('invalid_reasons', [])}")
                root_task.log += f"任务拆分验证失败，原因：{', '.join(validation_result.get('invalid_reasons', []))}\n"
                yield root_task.to_dict()
                
                # 使用修正后的子任务
                if validation_result.get("corrected_subtasks"):
                    subtasks_data = validation_result["corrected_subtasks"]
                    root_task.log += "已使用修正后的子任务列表\n"
                    yield root_task.to_dict()
                else:
                    raise Exception("任务拆分验证失败，且未提供修正后的子任务列表")
            else:
                root_task.log += "任务拆分验证通过\n"
                yield root_task.to_dict()

            # 添加新的校验逻辑
            is_valid, error_msg = TaskValidator.validate_subtasks(subtasks_data)
            if not is_valid:
                retry_count = self._increment_retry_counter('generate_subtasks', current_task.task_id)
                if TaskValidator.should_retry(retry_count):
                    logger.warning(f"子任务数据校验失败: {error_msg}, 第{retry_count}次重试")
                    yield from self.call_llm_generate_subtasks(
                        root_task, current_task, task_context, retrieve_datas,
                        history_messages, toc_user, user_location_info
                    )
                    return current_task.children
                else:
                    raise Exception(f"子任务生成失败，已达到最大重试次数: {error_msg}")

            # 移除大模型生成的tools字段，由retrieve_tools_for_task来处理
            for task_data in subtasks_data:
                if isinstance(task_data, dict):
                    task_data.pop('tools', None)
                    task_data.pop('execution_mode', None)  # 移除execution_mode字段，由工具检索结果决定
                else:
                    logger.error(f"任务数据格式错误: {task_data}")
                    raise Exception("任务数据格式错误，期望是字典类型")

            current_task.children.extend([TaskNode.from_dict(task_data) for task_data in subtasks_data])
        except Exception as e:
            logger.error(f"生成子任务时出错: {str(e)}")
            retry_count = self._increment_retry_counter('generate_subtasks', current_task.task_id)
            if TaskValidator.should_retry(retry_count):
                logger.warning(f"子任务数据校验失败, 第{retry_count}次重试")
                yield from self.call_llm_generate_subtasks(
                    root_task, current_task, task_context, retrieve_datas,
                    history_messages, toc_user, user_location_info
                )
                return current_task.children
            else:
                raise Exception(f"子任务生成失败，已达到最大重试次数: {error_msg}")

    def _generate_task_tree_internal(
            self,
            current_task: TaskNode,
            root_task: TaskNode,
            max_depth: int = 3,
            current_depth: int = 0,
            task_context: List[Dict[str, Any]] = None,
            task_map: Dict[str, TaskNode] = None,
            total_tasks: int = 1,
            completed_tasks: int = 0,
            retrieve_datas: dict = None,
            toc_user: Optional[ToCUser] = None,
            history_messages: List[Dict[str, str]] = [],
            user_location_info: dict = None
    ) -> Generator[Dict[str, Any], None, TaskNode]:
        """
        内部函数：使用DFS生成任务树并分析依赖关系

        Args:
            current_task: 当前正在处理的任务节点
            root_task: 任务树的根节点
            max_depth: 最大深度限制
            current_depth: 当前深度
            task_context: 任务上下文信息
            task_map: 任务ID映射表
            total_tasks: 总任务数
            completed_tasks: 已完成任务数
            retrieve_datas: 检索到的数据列表
            toc_user: 可选的ToCUser对象，用于提取人名
            history_messages: 历史消息列表

        Yields:
            Dict[str, Any]: 包含任务生成进度的信息
        Returns:
            TaskNode: 最终生成的任务树根节点
        """

        logger.info(f"[PLAN_GEN] 开始处理任务: {current_task.task_name} (ID: {current_task.task_id})")
        logger.info(f"[PLAN_GEN] 当前深度: {current_depth}, 最大深度: {max_depth}")
        logger.info(f"[PLAN_GEN] 是否原子任务: {current_task.is_atomic}")

        if task_context is None:
            task_context = []
            task_context = task_context + [{
                "task_id": current_task.task_id,
                "task_name": current_task.task_name,
                "task_description": current_task.task_description,
                "expected_deliverables": current_task.expected_deliverables
            }]
        if task_map is None:
            task_map = {}

        # 更新任务映射表
        task_map[current_task.task_id] = current_task

        # 检查深度限制
        if current_depth >= max_depth:
            return

        # 如果不是原子任务，则继续分解
        if not current_task.is_atomic:
            # 对于非原子任务，设置execution_mode为null
            current_task.execution_mode = None
            # current_task.tools = []  # 非原子任务不需要tools
            current_task.task_status = TaskStatus.PENDING  # 确保非原子任务状态为PENDING

            # 校验执行模式
            is_valid, error_msg = TaskValidator.validate_execution_mode(current_task)
            if not is_valid:
                logger.warning(f"执行模式校验失败: {error_msg}")
                current_task.execution_mode = None


            yield from self.call_llm_generate_subtasks(
                root_task,
                current_task,
                task_context,
                retrieve_datas,
                history_messages,
                toc_user=toc_user,
                user_location_info=user_location_info
            )

            subtasks = current_task.children

            # 更新总任务数
            total_tasks += len(subtasks)

            # 重置该层级的计数器
            counter = [0]

            # 处理所有子任务
            for subtask in subtasks:
                # 设置子任务的ID和父节点引用
                subtask.task_id = generate_task_id(current_task.task_id, counter)
                subtask.parent_id = current_task.task_id
                subtask.task_status = TaskStatus.PENDING  # 确保子任务状态为PENDING
                task_context.append(subtask.to_dict())

                # 检查子任务的深度
                if current_depth + 1 >= max_depth:
                    subtask.is_atomic = True
                
                # 立即更新task_map
                task_map[subtask.task_id] = subtask
                logger.info(f"[PLAN_GEN] 添加任务到task_map: {subtask.task_id} ({subtask.task_name})")

            # 在分析依赖关系之前，验证task_map的完整性
            logger.info(f"[PLAN_GEN] 当前task_map中的任务: {[f'{k} ({v.task_name})' for k, v in task_map.items()]}")
            logger.info(f"[PLAN_GEN] 待分析依赖关系的子任务: {[f'{task.task_id} ({task.task_name})' for task in subtasks]}")

            # 验证所有子任务是否都在task_map中
            missing_tasks = [task.task_id for task in subtasks if task.task_id not in task_map]
            if missing_tasks:
                logger.warning(f"[PLAN_GEN] 发现未在task_map中的任务: {missing_tasks}")
                for task in subtasks:
                    if task.task_id not in task_map:
                        logger.info(f"[PLAN_GEN] 补充添加缺失的任务到task_map: {task.task_id} ({task.task_name})")
                        task_map[task.task_id] = task

            # 验证父子关系的完整性
            for task_id, task in task_map.items():
                if task.parent_id and task.parent_id not in task_map:
                    logger.warning(f"[PLAN_GEN] 任务 {task_id} ({task.task_name}) 的父任务 {task.parent_id} 不在task_map中")
                    parent_task = next((t for t in task_map.values() if t.task_id == task.parent_id), None)
                    if parent_task:
                        logger.info(f"[PLAN_GEN] 补充添加父任务到task_map: {parent_task.task_id} ({parent_task.task_name})")
                        task_map[parent_task.task_id] = parent_task

            # 递归处理子任务
            for subtask in subtasks:
                if not subtask.is_atomic:
                    yield from self._generate_task_tree_internal(
                        current_task=subtask,
                        root_task=root_task,
                        max_depth=max_depth,
                        current_depth=current_depth + 1,
                        task_context=task_context,
                        task_map=task_map,
                        total_tasks=total_tasks,
                        completed_tasks=completed_tasks,
                        retrieve_datas=retrieve_datas,
                        toc_user=toc_user,
                        user_location_info=user_location_info
                    )

            yield root_task.to_dict()

            # 一次性分析所有子任务之间的依赖关系
            full_response = ""
            root_task.log += f"正在分析任务依赖关系：{[task.task_name for task in subtasks]}\n"
            yield root_task.to_dict()
            for chunk in self.call_llm_analyze_dependencies(subtasks=subtasks, task_map=task_map, retrieve_datas=retrieve_datas, toc_user=toc_user):
                full_response += chunk
            root_task.log += f"任务:{[task.task_name for task in subtasks]} 依赖关系分析完成\n"

            yield root_task.to_dict()

            # 收集所有原子任务
            atomic_tasks = [task for task in subtasks if task.is_atomic]

            # 一次性分析所有原子任务的工具选择和参数
            if atomic_tasks:
                # 获取根任务中的可用文件
                available_files = root_task.files if hasattr(root_task, 'files') else []
                available_connections = root_task.connections
                available_knowledge_base = root_task.knowledge_base
                yield from call_llm_analyze_parameters(
                    root_task=root_task,
                    atomic_tasks=atomic_tasks,
                    task_map=task_map,
                    available_files=available_files,
                    available_connections=available_connections,
                    available_knowledge_bases=available_knowledge_base,
                    toc_user=toc_user,
                    retrieve_datas=retrieve_datas,
                    conversation_id=self.conversation_id
                )

                # 校验工具参数
                for task in atomic_tasks:
                    is_valid, error_msg = TaskValidator.validate_tool_parameters(
                        task=task,
                        available_files=available_files,
                        available_connections=available_connections
                    )
                    if not is_valid:
                        logger.warning(f"工具参数校验失败: {error_msg}")
                        retry_count = self._increment_retry_counter('analyze_parameters', task.task_id)
                        if TaskValidator.should_retry(retry_count):
                            logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析工具参数")
                            yield from call_llm_analyze_parameters(
                                root_task=root_task,
                                atomic_tasks=[task],
                                task_map=task_map,
                                available_files=available_files,
                                available_connections=available_connections,
                                available_knowledge_bases=available_knowledge_base,
                                toc_user=toc_user,
                                retrieve_datas=retrieve_datas,
                                conversation_id=self.conversation_id
                            )

                logger.info(f"atomic_tasks retrieve for tools:{atomic_tasks}")

                # 分析任务的监督人信息
                root_task.log += f"正在分析任务监督人信息：{[task.task_name for task in atomic_tasks]}\n"
                yield root_task.to_dict()
                yield from self.call_llm_analyze_supervisor(
                    root_task=root_task,
                    tasks=atomic_tasks,
                    task_map=task_map,
                    retrieve_datas=retrieve_datas,
                    toc_user=toc_user
                )

            # 在工具分配后添加校验
            for task in atomic_tasks:
                is_valid, error_msg = TaskValidator.validate_tools(task)
                if not is_valid:
                    retry_count = self._increment_retry_counter('analyze_tools', task.task_id)
                    if TaskValidator.should_retry(retry_count):
                        logger.warning(f"工具分配校验失败: {error_msg}, 第{retry_count}次重试")
                        yield from call_llm_analyze_parameters(
                            root_task=root_task,
                            atomic_tasks=atomic_tasks,
                            task_map=task_map,
                            available_files=available_files,
                            available_connections=available_connections,
                            available_knowledge_bases=available_knowledge_base,
                            toc_user=toc_user,
                            retrieve_datas=retrieve_datas,
                            conversation_id=self.conversation_id
                        )
                        break
                    else:
                        raise Exception(f"工具分配失败，已达到最大重试次数: {error_msg}")

                # 校验执行模式
                is_valid, error_msg = TaskValidator.validate_execution_mode(task)
                if not is_valid:
                    logger.warning(f"执行模式校验失败: {error_msg}")
                    if task.tools and any(tool.get('name') == 'human_task' for tool in task.tools):
                        task.execution_mode = 'async'
                    else:
                        task.execution_mode = None

            completed_tasks += 1
            logger.info(f"任务 {current_task.task_name} 分解完成")

            # 依赖关系已经在call_llm_analyze_dependencies中处理完成
            yield root_task.to_dict()

        return

    def generate_task_tree(
            self,
            root_task: TaskNode,
            inputs: Mapping[str, Any] = None,
            toc_user: Optional[ToCUser] = None,
            query: Optional[str] = None,
            dataset_ids: list = [],
            search_data: list = [],
            history_messages: list = [],
            resource_contents: Dict[str, Any] = None,  # 新增参数
            max_depth: int = 2
    ) -> Generator[Dict[str, Any], None, TaskNode]:
        """
        生成任务树并分析依赖关系

        Args:
            root_task: 任务树的根节点
            inputs: 输入参数，包含files字段
            toc_user: 用户信息
            query: 用户查询
            dataset_ids: 数据集ID列表
            search_data: 联网搜索结果
            history_messages: 历史消息
            resource_contents: 已提取的资源内容
            max_depth: 最大深度限制

        Yields:
            Dict[str, Any]: 包含任务生成进度的信息
        Returns:
            TaskNode: 最终生成的任务树根节点
        """

        logger.info("[PLAN_GEN] 开始生成任务树")
        logger.info(f"[PLAN_GEN] 根任务: {root_task.task_name}")
        logger.info(f"[PLAN_GEN] 最大深度: {max_depth}")
        logger.info(f"[PLAN_GEN] 数据集ID: {dataset_ids}")
        logger.info(f"[PLAN_GEN] 搜索数据: {json.dumps(search_data, ensure_ascii=False, indent=2)}")

        start_time = datetime.now()

        # 使用传入的资源内容
        retrieve_datas = {}
        if resource_contents:
            # 合并知识库内容
            if resource_contents.get('datasets'):
                retrieve_datas.update(resource_contents['datasets'])
            
            # 合并文件内容
            if resource_contents.get('files'):
                retrieve_datas.update(resource_contents['files'])

        # 添加search_data的summary到retrieve_datas中
        if search_data:
            # 只取前2个搜索结果
            for idx, item in enumerate(search_data[:2]):
                summary = item.get('summary', '')
                if summary:
                    search_key = item.get('source', f'search_{idx + 1}')
                    if search_key in retrieve_datas:
                        retrieve_datas[search_key].append(summary)
                    else:
                        retrieve_datas[search_key] = [summary]

        if history_messages:
            history_messages = [{"query": message.query, "response": message.answer} for message in history_messages]
        else:
            history_messages = []

        root_task.log += f"任务树开始生成\n"
        logger.info(f"任务树开始生成\n")
        yield root_task.to_dict()

        yield from self._generate_task_tree_internal(
            current_task=root_task,
            root_task=root_task,
            max_depth=max_depth,
            retrieve_datas=retrieve_datas,
            toc_user=toc_user,
            history_messages=history_messages,
            user_location_info=inputs.get("user_location_info")
        )

        # 生成调度信息
        try:
            yield from generate_schedule_for_task(task=root_task, toc_user=toc_user,
                                                  conversation_id=self.conversation_id)
            is_valid, error_msg = TaskValidator.validate_schedule(task=root_task)
            if not is_valid:
                # 检查重试次数
                retry_count = self._increment_retry_counter("generate_schedule_for_task", root_task.task_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次生成调度信息")
                    yield from generate_schedule_for_task(task=root_task, toc_user=toc_user,
                                                          conversation_id=self.conversation_id)
        except Exception as e:
            retry_count = self._increment_retry_counter("generate_schedule_for_task", root_task.task_id)
            if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                logger.info(f"[PLAN_GEN] 第 {retry_count} 次生成调度信息")
                yield from generate_schedule_for_task(task=root_task, toc_user=toc_user,
                                                      conversation_id=self.conversation_id)

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 生成PLAN耗时统计：总耗时: {duration:.2f}秒")
        logger.info("任务树生成完成")
        logger.info(
            f"任务树-----------------------------------\n: {root_task.to_dict()}\n-----------------------------------")

    def call_llm_analyze_dependencies(
            self,
            subtasks: List[TaskNode],
            task_map: Dict[str, TaskNode],
            retrieve_datas: Optional[Dict[str, List[str]]] = None,
            toc_user: Optional[ToCUser] = None
    ) -> Generator[str, None, Dict[str, Dict[str, List[str]]]]:
        """
        调用大模型一次性分析所有子任务之间的依赖关系，支持流式输出

        Args:
            subtasks: 需要分析依赖关系的子任务列表
            task_map: 任务ID映射表，包含所有任务的信息
            retrieve_datas: 检索到的数据字典，key为数据源名，value为内容列表
            toc_user: 可选的ToCUser对象，用于提取人名

        Yields:
            str: 流式输出的内容片段
        Returns:
            Dict[str, Dict[str, List[str]]]: 每个任务ID对应的依赖关系，包含sibling_dependencies和data_dependencies
        """
        if retrieve_datas is None:
            retrieve_datas = {}
        logger.info("[PLAN_GEN] 开始分析任务依赖关系")
        logger.info(f"[PLAN_GEN] 待分析子任务: {[task.task_name for task in subtasks]}")
        logger.info(
            f"[PLAN_GEN] 任务映射表: {json.dumps({k: v.task_name for k, v in task_map.items()}, ensure_ascii=False, indent=2)}")

        reference_information = ""
        if retrieve_datas:
            for source_name, items in retrieve_datas.items():
                reference_information += f"{source_name}:\n"
                if isinstance(items, list):
                    for item in items:
                        reference_information += f"{item}\n"
                else:
                    reference_information += f"{items}"


        parent_task = task_map[subtasks[0].parent_id]
        system_prompt = """You are an expert task dependency analyzer with deep knowledge of workflow optimization and task sequencing. Your role is to:
    
    1. Analyze task dependencies and relationships
    2. Identify data flow requirements between tasks
    3. Determine execution order constraints
    4. Optimize for parallel execution where possible
    5. Ensure no circular dependencies
    6. Return responses in valid JSON format only
    
    Key Responsibilities:
    - Analyze task inputs and outputs
    - Identify true data dependencies
    - Consider parallel execution opportunities
    - Validate dependency relationships
    - Prevent circular dependencies
    - Optimize task sequencing
    
    Core Principles:
    1. Task Hierarchy:
       - Maintain clear parent-child relationships
       - Respect task boundaries and scope
       - Consider the complete task structure
    
    2. Dependency Types:
       - Data Dependencies: Track actual data flow between tasks
       - Execution Dependencies: Consider logical execution order
       - Resource Dependencies: Account for shared resource usage
       - Dataset Dependencies: Consider dataset processing sequence
    
    3. Validation Rules:
       - Ensure dependencies are logically sound
       - Prevent circular dependencies
       - Validate against task hierarchy
       - Data dependencies must come from atomic tasks
       - Non-atomic tasks cannot be data sources
    
    4. Temporal Considerations:
       - Respect chronological sequence requirements
       - Handle phase-based dependencies (prep, execution, evaluation)
       - Consider time-sensitive data processing needs
       - Manage dataset processing order
    """

        prompt = f"""
    Task Dependency Analysis Request:
    
    TASK PARENT INFO:
    {chr(10).join([
        f"  ID: {parent_task.task_id}" + chr(10) +
        f"  Name: {parent_task.task_name}" + chr(10) +
        f"  Description: {parent_task.task_description}" + chr(10) +
        f"  Expected Output: {parent_task.expected_deliverables}"
    ])}
    
    TASKS TO ANALYZE:
    (The tasks below are the subtasks of the parent task)
    {chr(10).join([
        f"Task {i+1}:" + chr(10) +
        f"  ID: {task.task_id}" + chr(10) +
        f"  Name: {task.task_name}" + chr(10) +
        f"  Description: {task.task_description}" + chr(10) +
        f"  Expected Output: {task.expected_deliverables}" + chr(10) +
        f"  Parent ID: {task.parent_id}" + chr(10) +
        f"  Is Atomic: {task.is_atomic}"
        for i, task in enumerate(subtasks)
    ])}
    
    TASK CONTEXT:
    {chr(10).join([
        f"Context Task {i+1}:" + chr(10) +
        f"  ID: {task_id}" + chr(10) +
        f"  Name: {task.task_name}" + chr(10) +
        f"  Description: {task.task_description}" + chr(10) +
        f"  Expected Output: {task.expected_deliverables}" + chr(10) +
        f"  Parent ID: {task.parent_id}" + chr(10) +
        f"  Is Atomic: {task.is_atomic}"
        for i, (task_id, task) in enumerate(task_map.items())
        if task not in subtasks
    ])}

    REFERENCE INFORMATION:
    {reference_information}

    REFERENCE INFORMATION PROCESSING REQUIREMENTS:
    1. Always prioritize following the order and structure presented in reference information
    2. Pay special attention to temporal indicators in reference information (before, after, first, then, finally, etc.)
    3. Convert temporal relationships into dependencies even when explicit data dependencies aren't apparent
    4. Particularly note any task execution sequences, processes, or workflows mentioned in reference information

    IMPORTANT: If REFERENCE INFORMATION is not empty, you MUST prioritize and closely follow the structure, order, and content provided in the REFERENCE INFORMATION section. Use the hierarchy, sequence, and grouping in REFERENCE INFORMATION as the primary basis for determining dependencies and execution order.

    DATA FLOW REQUIREMENTS:
    1. Data Dependencies:
       - Identify specific data outputs required by each task
       - Ensure data dependencies only come from atomic tasks
       - Consider data transformation requirements between tasks
       - Verify data format compatibility between tasks

    2. Execution Flow:
       - Identify tasks that can be executed in parallel
       - Specify mandatory sequential operations
       - Minimize unnecessary execution dependencies
       - Consider resource constraints and data processing order

    3. Cross-Level Data Flow:
       - Consider how parent task data flows to subtasks
       - Ensure data consistency across task levels
       - Define clear data aggregation points

    VALIDATION CHECKLIST:
    1. For each task's data dependency:
       - Verify the source task produces the required data
       - Ensure all required inputs have corresponding dependencies

    2. For execution dependencies:
       - Confirm they are at the same hierarchical level
       - Verify they are necessary for data flow
       - Check for opportunities to increase parallelism
    
    Please analyze the dependencies between these tasks and return a JSON object where:
    - Keys are task IDs
    - Values are objects containing:
      {{
        "sibling_dependencies": ["task_id1", "task_id2"],  // Tasks at the same hierarchical level that must complete before this task can start
        "data_dependencies": ["task_id2_2", "task_id4"]     // Tasks whose output this task needs
      }}
    
    Dependency Analysis Guidelines:
    1. Task Hierarchy Analysis:
       - Consider the complete task structure
       - Understand parent-child relationships
       - Respect task boundaries and scope
       - Identify task roles (atomic vs non-atomic)
    
    2. Data Flow Analysis:
       - Identify actual data requirements between tasks
       - Ensure data dependencies come from atomic tasks only
       - For non-atomic task outputs, identify the specific atomic subtasks that produce the needed data
       - Consider data transformation and processing needs
    
    3. Execution Order Analysis:
       - Determine logical execution sequence
       - Consider parallel execution opportunities
       - Identify mandatory sequential operations
       - Account for resource constraints
    
    4. Dependency Rules:
       DATA FLOW RULES:
       - Only atomic tasks can have data dependencies
       - Only atomic tasks can be data sources
       - A non-atomic task MUST NOT have any data dependencies
       - A non-atomic task MUST NOT be referenced as a data dependency
       - Data dependencies CAN cross different levels (a task can depend on any atomic task's output)
       
       EXECUTION FLOW RULES:
       - Sibling dependencies MUST ONLY refer to tasks that share the same parent_id as the current task.
       - Every sibling dependency should have a clear logical reason, based on sequential execution requirements at that level.
       - Avoid unnecessary dependencies that could limit parallelism
    
    5. Validation Requirements:
       - Ensure no circular dependencies
       - Validate all dependencies against task hierarchy
       - Verify data flow consistency
       - Check execution order logic
    
    Please return only valid JSON without any additional text or explanations.
    """

        # 收集所有流式输出
        full_response = ""

        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"[PLAN_GEN] 开始调用大模型分析依赖关系 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

        for chunk in llm_client.call_deepseek(prompt=prompt, system_prompt=system_prompt, toc_user=toc_user, conversation_id=self.conversation_id):
            full_response += chunk
            yield chunk

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 大模型分析依赖关系完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"[PLAN_GEN] 大模型分析依赖关系耗时: {duration:.2f}秒")

        logger.info("[PLAN_GEN] 大模型返回结果:")
        logger.info(full_response)

        # 处理完整的响应
        try:
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            dependencies = json.loads(full_response)
            logger.info("[PLAN_GEN] 解析后的依赖关系:")
            logger.info(json.dumps(dependencies, ensure_ascii=False, indent=2))

            if not isinstance(dependencies, dict):
                raise Exception("大模型返回的依赖关系格式错误")

            # 校验依赖关系
            is_valid, error_msg = TaskValidator.validate_dependencies(dependencies, task_map)
            if not is_valid:
                logger.warning(f"依赖关系校验失败: {error_msg}, 将重新生成")
                # 检查重试次数
                retry_count = self._increment_retry_counter("call_llm_analyze_dependencies", subtasks[0].parent_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析依赖关系")
                    # 递归调用自身重新生成
                    yield from self.call_llm_analyze_dependencies(subtasks, task_map, retrieve_datas, toc_user)
                    return
                else:
                    logger.warning(f"[PLAN_GEN] 依赖关系分析失败，已达到最大重试次数，尝试修复依赖关系")
                    # 尝试修复依赖关系
                    fixed_deps, fix_logs = TaskValidator.fix_invalid_dependencies(dependencies, task_map)
                    for log in fix_logs:
                        logger.info(f"[PLAN_GEN] 依赖关系修复: {log}")
                    dependencies = fixed_deps

            # 验证并处理每个任务的依赖关系
            for task in subtasks:
                task_deps = dependencies.get(task.task_id, {})
                data_deps = task_deps.get("data_dependencies", [])
                sibling_deps = task_deps.get("sibling_dependencies", [])

                logger.info(f"[PLAN_GEN] 开始处理任务 {task.task_id} ({task.task_name}) 的依赖关系")
                logger.info(f"[PLAN_GEN] 原始数据依赖: {data_deps}")
                logger.info(f"[PLAN_GEN] 原始执行依赖: {sibling_deps}")

                # 使用TaskValidator验证并过滤依赖关系
                valid_data_deps, valid_sibling_deps, warnings = TaskValidator.validate_and_filter_dependencies(
                    task_id=task.task_id,
                    data_deps=data_deps,
                    sibling_deps=sibling_deps,
                    task_map=task_map,
                    dependencies=dependencies
                )

                # 记录警告信息
                for warning in warnings:
                    logger.warning(f"[PLAN_GEN] {warning}")

                # 更新依赖关系
                task.data_dependencies = valid_data_deps
                task.sibling_dependencies = valid_sibling_deps

                logger.info(f"[PLAN_GEN] 任务 {task.task_id} ({task.task_name}) 依赖关系处理完成")
                logger.info(f"[PLAN_GEN] 有效数据依赖: {valid_data_deps}")
                logger.info(f"[PLAN_GEN] 有效执行依赖: {valid_sibling_deps}")

            yield ""
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}")
            logger.error(f"大模型返回的内容: {full_response}")
            retry_count = self._increment_retry_counter("call_llm_analyze_dependencies", subtasks[0].parent_id)
            if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析依赖关系")
                # 递归调用自身重新生成
                yield from self.call_llm_analyze_dependencies(subtasks, task_map, retrieve_datas, toc_user)
                return
            else:
                raise Exception(f"解析大模型返回的JSON失败: {str(e)}")
        except Exception as e:
            logger.error(f"分析依赖关系时发生错误: {str(e)}")
            raise e