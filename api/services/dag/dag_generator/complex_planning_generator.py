import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Op<PERSON>, Tu<PERSON>, Generator, Mapping, Set

from models.toc_user import ToCUser
from .complex_planning_base_generator import (ComplexPlanningBaseGenerator, TaskNode, TaskStatus, generate_task_id,
                                              call_llm_analyze_parameters,
                                              generate_schedule_for_task, extract_inputs)
from .util import McpTools, llm_client
from .task_validator import TaskValidator

logger = logging.getLogger(__name__)

class ComplexPlanningGenerator(ComplexPlanningBaseGenerator):

    def __init__(self, conversation_id: str):
        super().__init__(conversation_id)
        self._retry_counters = {}  # 用于存储重试次数的字典

    def _get_retry_key(self, func_name: str, task_id: str) -> str:
        """
        生成重试计数器的键

        Args:
            func_name: 函数名
            task_id: 任务ID

        Returns:
            str: 重试计数器的键
        """
        return f"{func_name}_{task_id}"

    def _increment_retry_counter(self, func_name: str, task_id: str) -> int:
        """
        增加重试计数器的值

        Args:
            func_name: 函数名
            task_id: 任务ID

        Returns:
            int: 当前重试次数
        """
        key = self._get_retry_key(func_name, task_id)
        self._retry_counters[key] = self._retry_counters.get(key, 0) + 1
        return self._retry_counters[key]

    def call_llm_generate_subtasks(self,
                                   root_task: TaskNode, current_task: TaskNode,
                                   task_context: List[Dict[str, Any]] = None,
                                   history_messages: List[Dict[str, str]] = [],
                                   toc_user: Optional[ToCUser] = None,
                                   user_location_info: dict = None) -> Generator[str, None, List[TaskNode]]:
        """修改提示词，让大模型一次性生成所有子任务及其依赖关系，支持流式输出"""
        context_desc = ""
        if task_context:
            context_desc = "\n任务上下文（从根节点到当前节点）：\n"
            for task in task_context:
                context_desc += f"- 任务ID: {task['task_id']}\n"
                context_desc += f"  名称: {task['task_name']}\n"
                context_desc += f"  描述: {task['task_description']}\n"
                context_desc += f"  预期输出: {task['expected_deliverables']}\n"
                context_desc += "\n"

        available_tools = McpTools.retrieve_tools_for_task(current_task.task_description)
        logger.info(f"[PLAN_GEN] 可用工具列表: {json.dumps(available_tools, ensure_ascii=False, indent=2)}")

        system_prompt = """You are an expert task decomposition specialist with deep knowledge of project management and workflow optimization. Your role is to break down complex tasks into well-structured subtasks while considering dependencies, parallelization opportunities, and atomicity. You must:
    
    1. Analyze task requirements thoroughly
    2. Identify logical task boundaries
    3. Consider dependencies between subtasks
    4. Optimize for parallel execution where possible
    5. Ensure atomic tasks are truly atomic
    6. Maintain clear and concise task descriptions
    7. Return responses in valid JSON format only
    
    Remember to:
    - Be precise in task descriptions
    - Consider all available context and resources
    - Validate dependencies carefully
    - Ensure task atomicity is appropriate
    - Optimize for efficient execution
    - Always return a valid JSON array enclosed in square brackets
    - Never include any text outside the JSON array
    """

        prompt = f"""
    Task Decomposition Request:
    
    CURRENT TASK:
    Task_Id: {current_task.task_id}
    Name: {current_task.task_name}
    Description: {current_task.task_description}
    
    AVAILABLE RESOURCES:
    Files: {[file['file_id'] for file in root_task.files] if hasattr(root_task, 'files') and root_task.files else []}
    Database Connections: {[connection['table'] for connection in root_task.connections] if hasattr(root_task, 'connections') and root_task.connections else []}
    
    TASK CONTEXT:
    {context_desc}
    
    Available tools:
    {available_tools}
    
    USER_INFO:
    {json.dumps(user_location_info)} // Contains user location (e.g., city, country), current local time, and other relevant details
    
    HISTORY USER MESSAGES (may be empty, just for reference):
    {[f"User: {msg['query']}{chr(10)}Assistant: {msg['response']}" for msg in history_messages]}
    
    CONTEXT ANALYSIS GUIDELINES:
    1.  **Understand Overall Context:** Carefully review the conversation history to grasp the overarching goals, evolving requirements, and general direction.
    2.  **Identify Key Information:** Refer to the history for any specific constraints, critical preferences, or previous decisions that should influence the current decomposition.
    3.  **Ensure Consistency:** Align subtasks with the established context and maintain consistency with prior task definitions or user feedback.
    4.  **Leverage User Information**: Use `USER_INFO` (e.g., location, current time) to tailor subtasks, such as accounting for time zones, local resource availability, or location-specific constraints, while ensuring consistency with reference information.

    OUTPUT FORMAT AND REQUIREMENTS:
    
    1. Format Requirements:
       - You MUST return a single, valid JSON array enclosed in square brackets []
       - Each element in the array must be a subtask object
       - Objects must be separated by commas with no trailing comma after the last object
       - DO NOT include any text, explanations, or comments outside the JSON array
       - The response must be parseable by standard JSON parsers
    
    2. Subtask Object Structure:   
    [
        {{
            "task_id": "string",              // Unique identifier for the subtask
            "task_name": "string",            // Concise description
            "task_description": "string",      // Detailed description with inputs
            "task_status": "Pending",         // Must always be "Pending"
            "is_atomic": boolean,             // True if cannot be broken down further
            "data_dependencies": ["string"],   // Array of task_ids this task depends on for data
            "sibling_dependencies": ["string"],// Array of task_ids this task depends on for execution
            "expected_deliverables": "string", // Expected outputs
            "start_time": "string",           // Optional ISO timestamp eg: 2025-06-18T15:20:00+09:00
            "completion_time": "string"        // Optional ISO timestamp eg: 2025-06-18T15:20:00+09:00
        }},
        ...
    ]
    
    Key Considerations:
    1. Task Dependencies:
       - Analyze and document all data dependencies
       - Identify parallel execution opportunities
       - Ensure no circular dependencies
       - Consider both sibling and cross-level dependencies
    
    2. Task Atomicity:
       - A task is atomic if:
         * It can be completed by a single natural person via the Human Task Tool or a single tool.
         * It has clear, measurable deliverables
         * It cannot be meaningfully broken down further
         * It has a single, well-defined purpose
    
    3. Task Descriptions:
       - Be specific and actionable
       - Include all necessary input information
       - Clearly state expected deliverables
       - Consider available resources and constraints
    
    4. Dependencies:
       - Document all data dependencies
       - Specify execution order requirements
       - Identify parallel execution opportunities
       - Ensure dependency relationships are valid
    
    5. Tools:
      - When splitting tasks, please follow the tools I provided as much as possible.
      - Ensure each atomic task is assigned to a single tool or a single natural person via the Human Task Tool, avoiding assignment to multiple tools or multiple persons.
      - If the original task information specifies that a natural person is to complete the task, assign it to that person via the Human Task Tool and clearly specify the assigned person in the task description.
    
    Please return only valid JSON array without any additional text or explanations.
    Ensure your response is in Chinese.
    """

        logger.info("[PLAN_GEN] 系统提示词:")
        logger.info(system_prompt)
        logger.info("[PLAN_GEN] 用户提示词:")
        logger.info(prompt)
        # 收集所有流式输出
        full_response = ""
        root_task.log += f"正在拆解任务：{current_task.task_name}\n"
        yield root_task.to_dict()

        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"[PLAN_GEN] 开始调用大模型生成子任务 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

        # 调用大模型
        for chunk in llm_client.call_deepseek(
            prompt=prompt,
            system_prompt=system_prompt,
            toc_user=toc_user,
            conversation_id=self.conversation_id
        ):
            full_response += chunk

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 大模型生成子任务完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"[PLAN_GEN] 大模型生成子任务耗时: {duration:.2f}秒")

        root_task.log += f"任务:{current_task.task_name} 拆解完成\n"
        yield root_task.to_dict()

        logger.info("[PLAN_GEN] 大模型返回结果:")
        logger.info(full_response)

        # 处理完整的响应
        try:
            # 清理可能存在的markdown代码块标记
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            subtasks_data = json.loads(full_response)
            logger.info("[PLAN_GEN] 解析后的子任务数据:")
            logger.info(json.dumps(subtasks_data, ensure_ascii=False, indent=2))

            # 确保 subtasks_data 是列表类型
            if not isinstance(subtasks_data, list):
                if isinstance(subtasks_data, dict):
                    # 如果是单个任务，转换为列表
                    subtasks_data = [subtasks_data]
                else:
                    logger.error(f"大模型返回的数据格式错误: {subtasks_data}")
                    raise Exception("大模型返回的数据格式错误，期望是任务列表")

            # 如果未拆解出来子任务则自动重试
            if not subtasks_data or len(subtasks_data) == 0:
                retry_count = self._increment_retry_counter("call_llm_generate_subtasks", current_task.task_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试生成子任务")
                    # 递归调用自身重新生成
                    yield from self.call_llm_generate_subtasks(root_task, current_task, task_context,
                                                               history_messages,
                                                               toc_user)
                    return current_task.children

            # 移除大模型生成的tools字段, 并将拆解后的任务添加到上下文中
            for task_data in subtasks_data:
                if isinstance(task_data, dict):
                    task_data.pop('tools', None)
                    task_data.pop('execution_mode', None)  # 移除execution_mode字段，由工具检索结果决定
                else:
                    logger.error(f"任务数据格式错误: {task_data}")
                    raise Exception("任务数据格式错误，期望是字典类型")

            # 校验子任务数据
            is_valid, error_msg = TaskValidator.validate_subtasks(subtasks_data)
            if not is_valid:
                logger.warning(f"子任务数据校验失败: {error_msg}, 将重新生成")
                # 检查重试次数
                retry_count = self._increment_retry_counter("call_llm_generate_subtasks", current_task.task_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试生成子任务")
                    # 递归调用自身重新生成
                    yield from self.call_llm_generate_subtasks(root_task, current_task, task_context, history_messages, toc_user)
                    return current_task.children
                else:
                    logger.error(f"[PLAN_GEN] 子任务生成失败，已达到最大重试次数: {error_msg}")
                    raise Exception(f"子任务生成失败，已达到最大重试次数: {error_msg}")

            current_task.children.extend([TaskNode.from_dict(task_data) for task_data in subtasks_data])
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}")
            logger.error(f"大模型返回的内容: {full_response}")

            retry_count = self._increment_retry_counter("call_llm_generate_subtasks", current_task.task_id)
            if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试生成子任务")
                # 递归调用自身重新生成
                yield from self.call_llm_generate_subtasks(root_task, current_task, task_context, history_messages,
                                                           toc_user)
                return current_task.children
            else:
                logger.error(f"[PLAN_GEN] 子任务生成失败，已达到最大重试次数: {error_msg}")
                raise Exception(f"子任务生成失败，已达到最大重试次数: {error_msg}")


    def _generate_task_tree_internal(
        self,
        current_task: TaskNode,
        root_task: TaskNode,
        max_depth: int = 3,
        current_depth: int = 0,
        task_context: List[Dict[str, Any]] = None,
        task_map: Dict[str, TaskNode] = None,
        total_tasks: int = 1,
        completed_tasks: int = 0,
        retrieve_datas: list = [],
        toc_user: Optional[ToCUser] = None,
        history_messages: List[Dict[str, str]] = [],
        user_location_info: dict = None
    ) -> Generator[Dict[str, Any], None, TaskNode]:

        logger.info(f"[PLAN_GEN] 开始处理任务: {current_task.task_name} (ID: {current_task.task_id})")
        logger.info(f"[PLAN_GEN] 当前深度: {current_depth}, 最大深度: {max_depth}")
        logger.info(f"[PLAN_GEN] 是否原子任务: {current_task.is_atomic}")

        start_time = datetime.now()

        if task_context is None:
            task_context = []
            task_context = task_context + [{
                "task_id": current_task.task_id,
                "task_name": current_task.task_name,
                "task_description": current_task.task_description,
                "expected_deliverables": current_task.expected_deliverables
            }]
        if task_map is None:
            task_map = {}

        # 更新任务映射表
        task_map[current_task.task_id] = current_task

        def _update_task_map(task: TaskNode):
            """
            更新task_map，确保任务关系的完整性
            """
            task_map[task.task_id] = task
            # 确保父子关系的完整性
            if task.parent_id and task.parent_id in task_map:
                parent_task = task_map[task.parent_id]
                if task not in parent_task.children:
                    parent_task.children.append(task)

        def _validate_task_map():
            """
            验证task_map中的任务关系是否完整
            """
            for task_id, task in task_map.items():
                if task.parent_id and task.parent_id not in task_map:
                    logger.warning(f"[PLAN_GEN] 任务 {task_id} 的父任务 {task.parent_id} 不在task_map中")
                    return False
                if task.children:
                    for child in task.children:
                        if child.task_id not in task_map:
                            logger.warning(f"[PLAN_GEN] 任务 {task_id} 的子任务 {child.task_id} 不在task_map中")
                            return False
            return True

        # 检查深度限制
        if current_depth >= max_depth:
            current_task.is_atomic = True  # 强制设置为原子任务
            _update_task_map(current_task)
            return

        # 如果不是原子任务，则继续分解
        if not current_task.is_atomic:
            current_task.execution_mode = None
            current_task.task_status = TaskStatus.PENDING

            # 校验执行模式
            is_valid, error_msg = TaskValidator.validate_execution_mode(current_task)
            if not is_valid:
                logger.warning(f"执行模式校验失败: {error_msg}")
                current_task.execution_mode = None


            yield from self.call_llm_generate_subtasks(
                root_task,
                current_task,
                task_context,
                history_messages,
                toc_user=toc_user,
                user_location_info=user_location_info
            )

            subtasks = current_task.children
            total_tasks += len(subtasks)
            counter = [0]

            # 处理所有子任务
            for subtask in subtasks:
                subtask.task_id = generate_task_id(current_task.task_id, counter)
                subtask.parent_id = current_task.task_id
                subtask.task_status = TaskStatus.PENDING
                task_context.append(subtask.to_dict())
                if current_depth + 1 >= max_depth:
                    subtask.is_atomic = True

                # 更新task_map，确保父子关系完整
                _update_task_map(subtask)

            # 递归处理子任务
            for subtask in subtasks:
                if not subtask.is_atomic:
                    yield from self._generate_task_tree_internal(
                        current_task=subtask,
                        root_task=root_task,
                        max_depth=max_depth,
                        current_depth=current_depth + 1,
                        task_context=task_context,
                        task_map=task_map,
                        total_tasks=total_tasks,
                        completed_tasks=completed_tasks,
                        retrieve_datas=retrieve_datas,
                        toc_user=toc_user,
                    )

            yield root_task.to_dict()

            # 在所有子任务处理完成后，更新当前任务到task_map
            _update_task_map(current_task)

            # 在分析依赖关系之前，验证task_map的完整性
            if not _validate_task_map():
                logger.error("[PLAN_GEN] task_map 验证失败，任务关系不完整")
                # 尝试修复任务关系
                for task_id, task in task_map.items():
                    if task.parent_id and task.parent_id not in task_map:
                        parent_task = next((t for t in task_map.values() if t.task_id == task.parent_id), None)
                        if parent_task:
                            _update_task_map(parent_task)
                    for child in task.children:
                        if child.task_id not in task_map:
                            _update_task_map(child)

            logger.info(f"[PLAN_GEN] 当前task_map中的任务: {[task_id for task_id in task_map.keys()]}")
            logger.info(f"[PLAN_GEN] 待分析依赖关系的子任务: {[task.task_id for task in subtasks]}")

            # 一次性分析所有子任务之间的依赖关系
            full_response = ""
            root_task.log += f"正在分析任务依赖关系：{[task.task_name for task in subtasks]}\n"
            yield root_task.to_dict()

            # 在分析依赖关系之前，确保所有任务都在task_map中
            for task in subtasks:
                if task.task_id not in task_map:
                    logger.warning(f"[PLAN_GEN] 添加缺失的任务到task_map: {task.task_id}")
                    _update_task_map(task)

            for chunk in self.call_llm_analyze_dependencies(subtasks=subtasks, task_map=task_map, toc_user=toc_user):
                full_response += chunk

            root_task.log += f"任务:{[task.task_name for task in subtasks]} 依赖关系分析完成\n"
            yield root_task.to_dict()

            # 收集所有原子任务
            atomic_tasks = [task for task in subtasks if task.is_atomic]

            # 一次性分析所有原子任务的工具选择和参数
            if atomic_tasks:
                available_files = root_task.files if hasattr(root_task, 'files') else []
                available_connections = root_task.connections

                yield from call_llm_analyze_parameters(
                    root_task=root_task,
                    atomic_tasks=atomic_tasks,
                    task_map=task_map,
                    available_files=available_files,
                    available_connections=available_connections,
                    toc_user=toc_user,
                    conversation_id=self.conversation_id)

                # 校验工具参数
                for task in atomic_tasks:
                    is_valid, error_msg = TaskValidator.validate_tool_parameters(
                        task=task,
                        available_files=available_files,
                        available_connections=available_connections
                    )
                    if not is_valid:
                        logger.warning(f"工具参数校验失败: {error_msg}")
                        retry_count = self._increment_retry_counter('analyze_parameters', task.task_id)
                        if TaskValidator.should_retry(retry_count):
                            logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析工具参数")
                            yield from call_llm_analyze_parameters(
                                root_task=root_task,
                                atomic_tasks=[task],
                                task_map=task_map,
                                available_files=available_files,
                                available_connections=available_connections,
                                toc_user=toc_user,
                                conversation_id=self.conversation_id
                            )

                # 分析任务的监督人信息
                root_task.log += f"正在分析任务监督人信息：{[task.task_name for task in atomic_tasks]}\n"
                yield root_task.to_dict()
                yield from self.call_llm_analyze_supervisor(
                    root_task=root_task,
                    tasks=atomic_tasks,
                    task_map=task_map,
                    retrieve_datas=retrieve_datas,
                    toc_user=toc_user
                )

            # 在工具分配后添加校验
            for task in atomic_tasks:
                is_valid, error_msg = TaskValidator.validate_tools(task)
                if not is_valid:
                    logger.warning(f"工具分配校验失败: {error_msg}, 将重新生成")
                    yield from call_llm_analyze_parameters(
                        root_task=root_task,
                        atomic_tasks=atomic_tasks,
                        task_map=task_map,
                        available_files=available_files,
                        available_connections=available_connections,
                        toc_user=toc_user,
                        conversation_id=self.conversation_id
                    )
                    break

                # 校验执行模式
                is_valid, error_msg = TaskValidator.validate_execution_mode(task)
                if not is_valid:
                    logger.warning(f"执行模式校验失败: {error_msg}")
                    if task.tools and any(tool.get('name') == 'human_task' for tool in task.tools):
                        task.execution_mode = 'async'
                    else:
                        task.execution_mode = None

            # 记录结束时间并计算耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"[PLAN_GEN] 生成PLAN耗时统计：总耗时: {duration:.2f}秒")
            completed_tasks += 1
            logger.info(f"任务 {current_task.task_name} 分解完成")

            yield root_task.to_dict()
        else:
            # 如果是原子任务，更新到task_map
            _update_task_map(current_task)

        return

    def generate_task_tree(
        self,
        root_task: TaskNode,
        inputs: Mapping[str, Any] = None,
        toc_user: Optional[ToCUser] = None,
        query: Optional[str] = None,
        dataset_ids: list = [],
        search_data: list = [],
        history_messages: list = [],
        required_resources: Dict[str, list] = None,  # 添加统一的资源依赖字典参数
        max_depth: int = 2,
        resource_contents: Dict[str, Any] = None,
    ) -> Generator[Dict[str, Any], None, TaskNode]:
        """
        生成任务树并分析依赖关系

        Args:
            root_task: 任务树的根节点
            inputs: 输入参数，包含files字段
            toc_user: 用户信息
            search_data: 联网搜索结果
            max_depth: 最大深度限制

        Yields:
            Dict[str, Any]: 包含任务生成进度的信息
        Returns:
            TaskNode: 最终生成的任务树根节点
        """

        logger.info("[PLAN_GEN] 开始生成任务树")
        logger.info(f"[PLAN_GEN] 根任务: {root_task.task_name}")
        logger.info(f"[PLAN_GEN] 最大深度: {max_depth}")
        logger.info(f"[PLAN_GEN] 数据集ID: {dataset_ids}")
        logger.info(f"[PLAN_GEN] 搜索数据: {json.dumps(search_data, ensure_ascii=False, indent=2)}")

        files, connections = extract_inputs(inputs)
        root_task.files = files
        root_task.connections = connections

        retrieve_datas = []

        # 添加search_data的summary到retrieve_datas中
        if search_data:
            # 只取前2个搜索结果
            search_summaries = [item.get('summary', '') for item in search_data[:2] if item.get('summary')]
            if search_summaries:
                retrieve_datas.append(search_summaries)

        if history_messages:
            history_messages = [{"query": message.query, "response": message.answer} for message in history_messages]
        else:
            history_messages = []

        logger.info(f"files:{files}, connections:{connections}, toc_user:{toc_user}")

        root_task.log += f"任务树开始生成\n"
        logger.info(f"任务树开始生成\n")
        yield root_task.to_dict()

        yield from self._generate_task_tree_internal(
            current_task=root_task,
            root_task=root_task,
            max_depth=max_depth,
            retrieve_datas=retrieve_datas,
            toc_user=toc_user,
            history_messages=history_messages,
            user_location_info=inputs.get("user_location_info")
        )

        # 生成调度信息
        max_retries = 3
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                logger.info(f"[PLAN_GEN] 开始生成调度信息，任务ID: {root_task.task_id}，第 {retry_count + 1} 次尝试")
                yield from generate_schedule_for_task(task=root_task, toc_user=toc_user, conversation_id=self.conversation_id)
                
                # 校验调度信息
                is_valid, error_msg = TaskValidator.validate_schedule(task=root_task)
                if not is_valid:
                    logger.warning(f"[PLAN_GEN] 调度信息校验失败: {error_msg}")
                    if retry_count < max_retries:
                        retry_count += 1
                        logger.info(f"[PLAN_GEN] 将进行第 {retry_count + 1} 次重试")
                        continue
                    else:
                        logger.error(f"[PLAN_GEN] 生成调度信息失败，已达到最大重试次数: {error_msg}")
                        raise Exception(f"生成调度信息失败，已达到最大重试次数: {error_msg}")
                
                # 如果成功，跳出循环
                logger.info(f"[PLAN_GEN] 调度信息生成成功")
                break
                
            except Exception as e:
                logger.error(f"[PLAN_GEN] 生成调度信息时发生异常: {str(e)}")
                
                if retry_count < max_retries:
                    retry_count += 1
                    logger.info(f"[PLAN_GEN] 将进行第 {retry_count + 1} 次重试")
                    continue
                else:
                    logger.error(f"[PLAN_GEN] 生成调度信息失败，已达到最大重试次数: {str(e)}")
                    raise Exception(f"生成调度信息失败，已达到最大重试次数: {str(e)}")
        
        logger.info("任务树生成完成")
        # logger.info(f"任务树-----------------------------------\n: {root_task.to_dict()}\n-----------------------------------")


    @staticmethod
    def validate_dependencies(task_node: TaskNode) -> Tuple[bool, List[str]]:
        """
        验证任务依赖关系的有效性

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        task_map = {}

        # 建立任务ID映射
        def build_task_map(node: TaskNode):
            task_map[node.task_id] = node
            for child in node.children:
                build_task_map(child)

        build_task_map(task_node)

        def validate_node(node: TaskNode) -> bool:
            valid = True

            # 1. 验证同级依赖的有效性
            if node.parent_id:
                # 获取所有同级任务
                siblings = [task for task in task_map.values() if task.parent_id == node.parent_id and task.task_id != node.task_id]
                sibling_ids = {s.task_id for s in siblings}
                for dep_id in node.sibling_dependencies:
                    if dep_id not in sibling_ids:
                        errors.append(f"任务 {node.task_id} 依赖了不存在的同级任务 {dep_id}")
                        valid = False

            # 2. 验证数据依赖的有效性
            for dep_id in node.data_dependencies:
                if dep_id not in task_map:
                    errors.append(f"任务 {node.task_id} 依赖了不存在的任务 {dep_id}")
                    valid = False

            # 3. 验证同级数据依赖是否在执行依赖中
            if node.parent_id:
                # 获取所有同级任务
                siblings = [task for task in task_map.values() if task.parent_id == node.parent_id and task.task_id != node.task_id]
                sibling_ids = {s.task_id for s in siblings}
                for dep_id in node.data_dependencies:
                    if dep_id in sibling_ids and dep_id not in node.sibling_dependencies:
                        errors.append(f"任务 {node.task_id} 的数据依赖 {dep_id} 未在执行依赖中声明")
                        valid = False

            # 4. 检查循环依赖
            visited = set()
            path = set()

            def has_cycle(current_id: str) -> bool:
                if current_id in path:
                    errors.append(f"检测到循环依赖，涉及任务 {current_id}")
                    return True
                if current_id in visited:
                    return False

                path.add(current_id)
                visited.add(current_id)

                current_node = task_map.get(current_id)
                if current_node:
                    for dep_id in current_node.sibling_dependencies + current_node.data_dependencies:
                        if dep_id in task_map and has_cycle(dep_id):
                            return True

                path.remove(current_id)
                return False

            if has_cycle(node.task_id):
                valid = False

            # 递归验证子节点
            for child in node.children:
                if not validate_node(child):
                    valid = False

            return valid

        return validate_node(task_node), errors

    def call_llm_analyze_dependencies(
            self,
            subtasks: List[TaskNode],
            task_map: Dict[str, TaskNode],
            retrieve_datas: Optional[Dict[str, List[str]]] = None,
            toc_user: Optional[ToCUser] = None
    ) -> Generator[str, None, Dict[str, Dict[str, List[str]]]]:
        """
        调用大模型一次性分析所有子任务之间的依赖关系，支持流式输出

        Args:
            subtasks: 需要分析依赖关系的子任务列表
            task_map: 任务ID映射表，包含所有任务的信息
            retrieve_datas: 检索到的数据字典，key为数据源名，value为内容列表
            toc_user: 可选的ToCUser对象，用于提取人名

        Yields:
            str: 流式输出的内容片段
        Returns:
            Dict[str, Dict[str, List[str]]]: 每个任务ID对应的依赖关系，包含sibling_dependencies和data_dependencies
        """
        logger.info("[PLAN_GEN] 开始分析任务依赖关系")
        logger.info(f"[PLAN_GEN] 待分析子任务: {[task.task_name for task in subtasks]}")
        logger.info(
            f"[PLAN_GEN] 任务映射表: {json.dumps({k: v.task_name for k, v in task_map.items()}, ensure_ascii=False, indent=2)}")

        parent_task = task_map[subtasks[0].parent_id]
        system_prompt = """You are an AI expert specializing in detailed task dependency analysis and workflow optimization. Your core function is to generate accurate, logical, and optimized task dependencies based on the provided task information and explicit rules.

        Your primary goal is to ensure the generated dependencies strictly adhere to all specified constraints, validation criteria, and especially any priority guidelines provided for the specific task. You must always output valid JSON.
    """

        prompt = f"""
    Task Dependency Analysis Request:
    
    TASK PARENT INFO:
    {chr(10).join([
        f"  ID: {parent_task.task_id}" + chr(10) +
        f"  Name: {parent_task.task_name}" + chr(10) +
        f"  Description: {parent_task.task_description}" + chr(10) +
        f"  Expected Output: {parent_task.expected_deliverables}"
    ])}
    
    TASKS TO ANALYZE:
    (The tasks below are the subtasks of the parent task)
    {chr(10).join([
        f"Task {i+1}:" + chr(10) +
        f"  ID: {task.task_id}" + chr(10) +
        f"  Name: {task.task_name}" + chr(10) +
        f"  Description: {task.task_description}" + chr(10) +
        f"  Expected Output: {task.expected_deliverables}" + chr(10) +
        f"  Parent ID: {task.parent_id}" + chr(10) +
        f"  Is Atomic: {task.is_atomic}" + chr(10) +
        f"  *** IMPORTANT: {'This is an atomic task that can have data dependencies' if task.is_atomic else 'This is a non-atomic task that CANNOT have any data dependencies'} ***"
        for i, task in enumerate(subtasks)
    ])}
    
    TASK CONTEXT:
    {chr(10).join([
        f"Context Task {i+1}:" + chr(10) +
        f"  ID: {task_id}" + chr(10) +
        f"  Name: {task.task_name}" + chr(10) +
        f"  Description: {task.task_description}" + chr(10) +
        f"  Expected Output: {task.expected_deliverables}" + chr(10) +
        f"  Parent ID: {task.parent_id}" + chr(10) +
        f"  Is Atomic: {task.is_atomic}"
        f"  Data Dependencies: {task.data_dependencies}"
        f"  Sibling dependencies: {task.sibling_dependencies}"
        for i, (task_id, task) in enumerate(task_map.items())
        if task not in subtasks
    ])}
    
    Dependency Analysis Guidelines:

    This section provides the rules and guidelines for analyzing task dependencies, focusing on sibling dependencies and data flow across all levels. It also emphasizes aligning dependencies with the overall plan's objective.
    
    1. Understanding Task Hierarchy and Purpose:
    
    * Consider the complete task structure, including parent-child relationships.
    * Respect task boundaries and scope.
    * Actively consider each task's 'Description' and 'Expected Output' to understand its specific purpose.
    * Crucially, understand how each task contributes to the 'Parent Task''s overall 'Expected Output'.
    * Atomic Task: A fundamental, undividable execution unit that performs a specific operation and produces output. Atomic tasks are the only ones that can have data dependencies.
    * Non-Atomic Task: A composite task that orchestrates its child tasks. Non-atomic tasks cannot have data dependencies; they manage the flow of their subtasks.
    
    2. **CRITICAL DEPENDENCY RULES (STRICTLY ENFORCED):**
    
    * **NO CROSS-LEVEL SIBLING DEPENDENCIES:** Sibling dependencies **MUST ONLY** refer to tasks that share the **exact same `parent_id`** as the current task. **NEVER** create a sibling dependency between tasks from different hierarchical levels. This rule is paramount.
    * **NO CIRCULAR DEPENDENCIES:** There **MUST** be no circular dependencies. Ensure no task directly or indirectly depends on itself through any combination of sibling and/or data dependencies. **This rule is absolute and applies to the entire dependency graph.**
    
    3. Data Flow Analysis and Rules:
    
    * **Definition of Data Dependency:** A **data dependency** exists when a task *requires* the output, results, or specific information produced by a preceding atomic task to accurately perform its operation, derive new insights, or achieve its 'Expected Output'. This includes direct consumption, awareness of state, or utilizing content for final deliverables.
    * Source & Destination: Only atomic tasks can be data sources and only atomic tasks can have data dependencies.
    * Non-Atomic Tasks & Data: Non-atomic tasks MUST NOT have any data dependencies, nor can they be referenced as data dependency sources.
    * Cross-Level Data Flow: Data dependencies CAN cross different hierarchical levels (a task can depend on any atomic task's output).
    * **Information Aggregation Principle:** If an atomic task (e.g., Task C) *explicitly integrates, synthesizes, or consolidates* all relevant information or outputs from its preceding upstream atomic tasks (e.g., Task A, Task B) such that Task C's output fully encapsulates the combined knowledge of A and B, then any subsequent downstream task (e.g., Task D) that requires this combined knowledge **SHOULD ONLY** establish a data dependency on Task C, **NOT** on Task A or Task B directly. This prevents redundant dependencies and simplifies the graph.
    * **Logical Exclusion/Constraint Data Dependencies:** A data dependency **MUST** also be established when a task needs to be aware of the *results, state, or specific properties* of a previous atomic task's output. This is crucial for ensuring uniqueness, avoiding overlap, or adhering to derived constraints, even if the data is not directly transformed as an input. For example, if 'Generate Day 2 Tour Route' needs to know 'Day 1 Tour Route' to avoid visiting the same locations, 'Generate Day 2 Tour Route' has a data dependency on 'Day 1 Tour Route'.
    * **Content Integration/Final Output Dependencies:** For tasks whose primary purpose is to generate a final document, report, summary, or any structured deliverable (e.g., "生成...", "撰写...", "制作...", "输出..."), they **MUST** establish a data dependency on the atomic task(s) that produce the **core content, raw data, or complete information** that forms the substance of that final output. Even if the data is merely "read" or "formatted" rather than transformed, this constitutes a data dependency.
    
    4. Execution Flow Rules:
    
    * Sibling Dependencies: Sibling dependencies MUST ONLY refer to tasks that share the same parent_id as the current task.
    * Logical Sequencing: Every sibling dependency should have a clear logical reason based on sequential execution requirements at that level. This reason MUST align with the overall plan's flow and ultimately contribute to the Parent Task's Expected Output.
    * Temporal and Phased Dependencies: Pay critical attention to task names or descriptions containing "phase", "step", "before/during/after", numerical order (e.g., "Step 1", "Step 2"), or workflow patterns like "Preparation -> Execution -> Verification -> Summary". These indicate mandatory sequential dependencies that must be established regardless of explicit data transfer.
    * Optimize for Parallelism: Avoid unnecessary dependencies that could limit parallel execution.
    
    5. Examples:
    
    * Correct Data Dependency:
        * "task_A_atomic" (atomic) depends on data from "task_B_atomic" (atomic).
    * Correct Execution Dependency:
        * "task_C_non_atomic" (non-atomic) has sibling_dependencies but NO data_dependencies.
    * **Correct Logical Exclusion Data Dependency:**
        * "规划第二天旅游路线" (Generate Day 2 Tour Route) depends on "规划第一天旅游路线" (Generate Day 1 Tour Route) (data_dependency, because Day 2 needs to know Day 1's route to avoid overlap).
    * **Correct Content Integration Data Dependency (e.g., Document Generation):**
        * "最终生成游玩路线文档" (Final Generate Tour Route Document) depends on "制定游玩路线计划" (Formulate Tour Route Plan) (data_dependency, as the document generation task requires the *finalized, integrated plan content* from this single source).
    * Correct Temporal Dependency:
        * "Pre-livestream preparation" has a sibling_dependency on "Livestream execution".
        * "Livestream execution" has a sibling_dependency on "Post-livestream processing".
    * Incorrect Data Dependency (Non-atomic task as consumer):
        * "task_D_non_atomic" (non-atomic) depends on data from "task_E_atomic".
        * Reason: Non-atomic tasks cannot have data dependencies.
    * Incorrect Data Dependency (Non-atomic task as source):
        * "task_F_atomic" depends on data from "task_G_non_atomic" (non-atomic).
        * Reason: Only atomic tasks can be data sources.
    * **Incorrect Sibling Dependency (Cross-level violation):**
        * Task 'Subtask_A_1' (child of Task_A) has a sibling_dependency on Task 'B' (which is NOT a child of Task_A).
        * Reason: Sibling dependencies MUST be at the same hierarchical level.
    
    6. Validation Requirements:
    
    * **MANDATORY CHECK: NO CIRCULAR DEPENDENCIES.** Rigorously verify that no task directly or indirectly depends on itself through any combination of sibling and/or data dependencies. If a cycle is detected, re-evaluate and correct.
    * **MANDATORY CHECK: STRICT HIERARCHY ALIGNMENT.** All dependencies MUST strictly align with the defined task hierarchy. Specifically, sibling dependencies MUST NOT cross hierarchical levels. If a cross-level sibling dependency is found, it is an error.
    * Data Flow Consistency: Verify that data dependencies adhere strictly to the atomic task rules and capture all necessary data requirements. **This explicitly includes:**
        * **Logical Exclusion/Constraint Dependencies:** Ensure tasks that need to avoid overlap or be aware of prior results (e.g., "规划第二天旅游路线" 依赖 "规划第一天旅游路线" 以避免重复) 建立了正确的数据依赖。
        * **Content Integration/Final Output Dependencies:** For tasks identified as generating final documents, reports, or summaries, ensure they have a valid data dependency on the atomic task(s) that supply their core content (e.g., "最终生成游玩路线文档" 依赖 "制定游玩路线计划" 的输出内容)。
        * **Aggregation Point Adherence:** Critically check that if an upstream task clearly integrates or synthesizes information from multiple sources, downstream tasks relying on this *aggregated* information depend only on the *integrating task*, not redundantly on its original sub-sources.
    * Execution Logic: Confirm the logical soundness of the determined execution order, especially for identified temporal and phased sequences.
    * Overall Plan Alignment: Critically review the generated dependencies to ensure they logically enable the Parent Task's 'Expected Output' to be achieved efficiently and correctly.


    ---
    Output Format:
    
    Please analyze the dependencies between the "Tasks for Analysis" and any relevant "Additional Context Tasks". Return a JSON object where:
    
    * Keys: Task IDs (from "Tasks for Analysis").
    * Values: An object containing two lists:
        * "sibling_dependencies": List of task IDs at the same hierarchical level that must complete before this task starts.
        * "data_dependencies": List of task IDs whose atomic output this task requires to begin.
    
    Example Structure:
    {{
      "task_id_A_atomic": {{
        "sibling_dependencies": ["task_id_B", "task_id_C"],
        "data_dependencies": ["task_id_D_atomic", "task_id_E_atomic"]
      }},
      "task_id_F_atomic": {{
        "sibling_dependencies": [],
        "data_dependencies": ["task_id_G_atomic"]
      }}
    }}
    
    Please return only valid JSON without any additional text or explanations.
    """

        # 收集所有流式输出
        full_response = ""

        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"[PLAN_GEN] 开始调用大模型分析依赖关系 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

        for chunk in llm_client.call_deepseek(prompt=prompt, system_prompt=system_prompt, toc_user=toc_user, conversation_id=self.conversation_id):
            full_response += chunk
            yield chunk

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 大模型分析依赖关系完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"[PLAN_GEN] 大模型分析依赖关系耗时: {duration:.2f}秒")

        logger.info("[PLAN_GEN] 大模型返回结果:")
        logger.info(full_response)

        # 处理完整的响应
        try:
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            dependencies = json.loads(full_response)
            logger.info("[PLAN_GEN] 解析后的依赖关系:")
            logger.info(json.dumps(dependencies, ensure_ascii=False, indent=2))

            if not isinstance(dependencies, dict):
                raise Exception("大模型返回的依赖关系格式错误")

            # 校验依赖关系
            is_valid, error_msg = TaskValidator.validate_dependencies(dependencies, task_map)
            if not is_valid:
                logger.warning(f"依赖关系校验失败: {error_msg}, 将重新生成")
                # 检查重试次数
                retry_count = self._increment_retry_counter("call_llm_analyze_dependencies", subtasks[0].parent_id)
                if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                    logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析依赖关系")
                    # 递归调用自身重新生成
                    yield from self.call_llm_analyze_dependencies(subtasks=subtasks, task_map=task_map, toc_user=toc_user)
                    return
                else:
                    logger.warning(f"[PLAN_GEN] 依赖关系分析失败，已达到最大重试次数，尝试修复依赖关系")
                    # 尝试修复依赖关系
                    fixed_deps, fix_logs = TaskValidator.fix_invalid_dependencies(dependencies, task_map)
                    for log in fix_logs:
                        logger.info(f"[PLAN_GEN] 依赖关系修复: {log}")
                    dependencies = fixed_deps

            # 验证并处理每个任务的依赖关系
            for task in subtasks:
                task_deps = dependencies.get(task.task_id, {})
                data_deps = task_deps.get("data_dependencies", [])
                sibling_deps = task_deps.get("sibling_dependencies", [])

                logger.info(f"[PLAN_GEN] 开始处理任务 {task.task_id} ({task.task_name}) 的依赖关系")
                logger.info(f"[PLAN_GEN] 原始数据依赖: {data_deps}")
                logger.info(f"[PLAN_GEN] 原始执行依赖: {sibling_deps}")

                # 使用TaskValidator验证并过滤依赖关系
                valid_data_deps, valid_sibling_deps, warnings = TaskValidator.validate_and_filter_dependencies(
                    task_id=task.task_id,
                    data_deps=data_deps,
                    sibling_deps=sibling_deps,
                    task_map=task_map,
                    dependencies=dependencies
                )

                # 记录警告信息
                for warning in warnings:
                    logger.warning(f"[PLAN_GEN] {warning}")

                # 更新依赖关系
                task.data_dependencies = valid_data_deps
                task.sibling_dependencies = valid_sibling_deps

                logger.info(f"[PLAN_GEN] 任务 {task.task_id} ({task.task_name}) 依赖关系处理完成")
                logger.info(f"[PLAN_GEN] 有效数据依赖: {valid_data_deps}")
                logger.info(f"[PLAN_GEN] 有效执行依赖: {valid_sibling_deps}")

            yield ""
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}")
            logger.error(f"大模型返回的内容: {full_response}")
            logger.warning(f"依赖关系校验失败: {error_msg}, 将重新生成")
            # 检查重试次数
            retry_count = self._increment_retry_counter("call_llm_analyze_dependencies", subtasks[0].parent_id)
            if retry_count <= 3:  # 最多重试3次（包括第一次尝试）
                logger.info(f"[PLAN_GEN] 第 {retry_count} 次重试分析依赖关系")
                # 递归调用自身重新生成
                yield from self.call_llm_analyze_dependencies(subtasks=subtasks, task_map=task_map, toc_user=toc_user)
                return
            else:
                raise Exception(f"解析大模型返回的JSON失败: {str(e)}")
        except Exception as e:
            logger.error(f"分析依赖关系时发生错误: {str(e)}")
            raise e