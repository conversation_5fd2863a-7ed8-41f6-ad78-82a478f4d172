import copy  # 添加copy模块导入
import json
import logging
import re
import urllib.parse
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import List, Dict, Any, Optional, Generator, Mapping, Set

import requests

# from core.apollo_client import apollo_client
from configs.remote_settings_sources.apollo import apollo_client
from core.file import helpers as file_helpers
from models import db
from models.toc_user import ToCUser
from services.dag.dag_generator.util import llm_client, McpTools
from services.tools_summary_service import ToolsSummaryService

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    PENDING = "Pending"  # 待规划
    PLANNED = "Planned"  # 已规划
    IN_PROGRESS = "InProgress"  # 执行中
    COMPLETED = "Completed"  # 已完成
    FAILED = "Failed"  # 失败
    STOPPED = "Stopped"  # 停止


class ExecutionMode(Enum):
    ASYNC = "async"  # 异步执行，需要人工执行
    SYNC = "sync"  # 同步执行，可以快速完成或自动执行


class TaskNode:
    def __init__(
            self,
            task_id: str,
            task_name: str,
            task_description: str,
            is_atomic: bool,
            expected_deliverables: str,
            task_status: TaskStatus = TaskStatus.PENDING,  # 设置默认值为PENDING
            tools: Optional[List[Dict[str, str]]] = None,
            data_dependencies: Optional[List[str]] = None,
            sibling_dependencies: Optional[List[str]] = None,
            start_time: Optional[datetime] = None,
            completion_time: Optional[datetime] = None,
            children: Optional[List['TaskNode']] = None,
            execution_mode: ExecutionMode = ExecutionMode.SYNC,
            files: Optional[List[Dict[str, str]]] = None,
            connections: Optional[List[Dict[str, str]]] = None,
            schedule_interval: Optional[str] = None,
            log: Optional[str] = None,  # 修改log字段类型为string
            parent_id: Optional[str] = None,  # 添加parent_id字段
    ):
        self.task_id = task_id
        self.task_name = task_name
        self.task_description = task_description
        self.task_status = task_status
        self.is_atomic = is_atomic
        self.tools = tools or []
        self.data_dependencies = data_dependencies or []
        self.sibling_dependencies = sibling_dependencies or []
        self.expected_deliverables = expected_deliverables
        self.start_time = start_time
        self.completion_time = completion_time
        self.children = children or []
        self.parent_id = parent_id  # 替换parent对象为parent_id
        self.execution_mode = execution_mode
        self.files = files or []
        self.connections = connections or []  # 添加connections属性
        self.schedule_interval = schedule_interval
        self.log = log or ""  # 初始化log字段为空字符串
        self.supervisor = {}
        self.knowledge_base = []

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "task_id": self.task_id,
            "task_name": self.task_name,
            "task_description": self.task_description,
            "task_status": self.task_status.value,
            "is_atomic": self.is_atomic,
            "execution_mode": self.execution_mode.value if self.execution_mode else None,
            "data_dependencies": self.data_dependencies,
            "sibling_dependencies": self.sibling_dependencies,
            "expected_deliverables": self.expected_deliverables,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "completion_time": self.completion_time.isoformat() if self.completion_time else None,
            "parent_id": self.parent_id,  # 直接使用parent_id
            "files": self.files,
            "connections": self.connections,  # 添加connections到序列化结果
            "schedule_interval": self.schedule_interval,
            "log": self.log,  # 添加log字段到序列化结果,
            'supervisor': self.supervisor,
            'knowledge_base': self.knowledge_base,
        }
        if self.children:
            result["children"] = [child.to_dict() for child in self.children]
        if self.is_atomic:
            result["tools"] = self.tools
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskNode':
        children = [cls.from_dict(child) for child in data.get("children", [])]
        execution_mode_str = data.get("execution_mode")
        execution_mode = ExecutionMode(execution_mode_str) if execution_mode_str else None

        node = cls(
            task_id=data["task_id"],
            task_name=data["task_name"],
            task_description=data["task_description"],
            task_status=TaskStatus(data["task_status"]),
            is_atomic=data["is_atomic"],
            tools=data.get("tools", []),
            data_dependencies=data.get("data_dependencies", []),
            sibling_dependencies=data.get("sibling_dependencies", []),
            expected_deliverables=data["expected_deliverables"],
            start_time=datetime.fromisoformat(data["start_time"]) if data.get("start_time") else None,
            completion_time=datetime.fromisoformat(data["completion_time"]) if data.get("completion_time") else None,
            children=children,
            execution_mode=execution_mode,
            files=data.get("files", []),
            connections=data.get("connections", []),
            schedule_interval=data.get("schedule_interval"),
            log=data.get("log", ""),  # 从字典中读取log字段，默认为空字符串
            parent_id=data.get("parent_id"),  # 从字典中读取parent_id
        )
        # 设置子节点的parent_id
        for child in node.children:
            child.parent_id = node.task_id
        return node

def map_task_status(chinese_status: str) -> TaskStatus:
    """Map Chinese task status to English TaskStatus enum"""
    status_map = {
        "待规划": TaskStatus.PENDING,
        "已规划": TaskStatus.PLANNED,
        "执行中": TaskStatus.IN_PROGRESS,
        "已完成": TaskStatus.COMPLETED,
        "失败": TaskStatus.FAILED
    }
    return status_map.get(chinese_status, TaskStatus.PENDING)


def generate_task_id(parent_id: Optional[str] = None, counter: List[int] = [0]) -> str:
    """生成层级任务ID"""
    if parent_id is None or parent_id == "task_root":
        counter[0] += 1
        return f"task_{counter[0]}"
    else:
        counter[0] += 1
        return f"{parent_id}_{counter[0]}"

def extract_inputs(inputs: Mapping[str, Any]) -> Mapping[str, Any]:
    """
    提取输入参数中的files和connections
    """

    file_dicts = []
    files = inputs.get("files") or [] if inputs else []
    if files:
        for file in files:
            if file is None:
                continue
            # 兼容处理：如果 file 是对象且有 to_dict 方法，则调用；否则假定已是 dict
            file_dict = file.to_dict() if hasattr(file, 'to_dict') and callable(getattr(file, 'to_dict')) else file
            # 确保 file_dict 是字典类型
            if not isinstance(file_dict, dict):
                logger.error(f"Invalid file format: {file_dict}")
                continue
            # 复制一份以避免修改原始数据
            file_dict = file_dict.copy()
            file_dict['file_id'] = str(uuid.uuid4())
            if 'upload_file_id' in file_dict:
                file_dict['file_id'] = file_dict['upload_file_id']
                file_dict['url'] = file_helpers.get_signed_file_url(file_dict['upload_file_id'])
            file_dicts.append(file_dict)

    connections = []
    connection_list = inputs.get("connections") if inputs else []
    if connection_list:
        try:
            connections = json.loads(connection_list)
        except json.JSONDecodeError as e:
            logger.error(f"解析connections JSON失败: {str(e)}")
            logger.info(f"files:{files}, connections:{connection_list}")
            raise Exception(f"解析connections JSON失败: {str(e)}")

    remote_ip = inputs.get("remote_ip") if inputs else None
    logger.info(f"请求ip:{remote_ip}")

    return file_dicts, connections

def send_feishu_alarm(msg: str) -> bool:
    """
    发送飞书告警信息

    Args:
        msg: 告警信息内容

    Returns:
        bool: 是否发送成功
    """
    try:
        # 对消息进行URL编码
        encoded_msg = urllib.parse.quote(msg)
        url = f"https://gateway.ywwl.com/task-center/card/message/sendAlarm"

        # 使用POST方法，将消息作为参数传递
        data = {"msg": msg}
        headers = {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}
        response = requests.post(url, data=data, headers=headers)

        if response.status_code == 200:
            logger.info(f"飞书告警信息发送成功")
            return True
        else:
            logger.error(f"飞书告警信息发送失败: {response.status_code}, {response.text}")
            return False
    except Exception as e:
        logger.error(f"发送飞书告警信息异常: {str(e)}")
        return False


# 这里改掉调用http://**************:12005/mcp-servers 的接口 获取 MCP SERVER 工具列表
def retrieve_tools_for_task(task_description: str) -> List[Dict[str, str]]:
    """
    检索任务相关的系统工具

    Args:
        task_description: 任务描述

    Returns:
        List[Dict[str, str]]: 系统工具列表，每个工具包含tool_id, name, description, type

    Raises:
        Exception: 当没有找到合适的工具时抛出异常
    """
    tools = []

    # 检索系统工具
    try:
        import requests
        # 从Apollo配置中心获取域名
        try:
            gateway_domain = apollo_client.get_value(key="TASK_GATEWAY2_DOMAIN")
            # 检验域名是否有效
            if not gateway_domain or gateway_domain.lower() == "none":
                logger.warning("从Apollo获取到无效域名，使用默认值")
                gateway_domain = "test-gateway.ywwl.com/mcp-proxy"
        except Exception as apollo_error:
            logger.warning(f"从Apollo获取域名失败，使用默认值: {str(apollo_error)}")
            gateway_domain = "test-gateway.ywwl.com/mcp-proxy"

        logger.info(f"请求的URL: https://{gateway_domain}/mcpToolInfo?onlyData=true")
        response = requests.get(f"https://{gateway_domain}/mcpToolInfo?onlyData=true")
        if response.status_code == 200:
            response_data = response.json()
            # 兼容不同的响应结构
            # 处理可能包含 data 字段的结构
            if isinstance(response_data, dict) and 'data' in response_data:
                result = response_data.get('data', [])
            else:
                result = response_data

            # 确保result不为None，若为None则初始化为空列表
            if result is None:
                error_msg = "获取工具列表返回结果为None"
                logger.error(error_msg)
                try:
                    send_feishu_alarm(error_msg)
                except Exception as alarm_error:
                    logger.error(f"发送飞书告警失败: {str(alarm_error)}")
                result = []
        else:
            error_msg = f"获取工具列表失败: {response.status_code}, {response.text}"
            logger.error(error_msg)
            try:
                send_feishu_alarm(error_msg)
            except Exception as alarm_error:
                logger.error(f"发送飞书告警失败: {str(alarm_error)}")
            result = []
    except Exception as e:
        error_msg = f"HTTP请求获取工具列表失败: {str(e)}"
        logger.error(error_msg)
        # 调用发送告警信息的方法
        try:
            send_feishu_alarm(error_msg)
        except Exception as alarm_error:
            logger.error(f"发送飞书告警失败: {str(alarm_error)}")
        # 发生错误时，返回空列表而不是抛出异常，以提高系统健壮性
        result = []

    # try:
    #     # 打印检索结果
    #     logger.info(f"工具检索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    # except Exception as json_error:
    #     logger.error(f"打印工具检索结果失败: {str(json_error)}")

    # 确保后续处理中result是可迭代的
    if not isinstance(result, list):
        error_msg = f"MCP工具列表不是一个列表，实际类型: {type(result)},https://{gateway_domain}/mcpToolInfo?onlyData=true"
        logger.error(error_msg)
        try:
            send_feishu_alarm(error_msg)
        except Exception as alarm_error:
            logger.error(f"发送飞书告警失败: {str(alarm_error)},https://{gateway_domain}/mcpToolInfo?onlyData=true")
        result = []

    # 检索系统工具
    try:
        service = ToolsSummaryService(db.session)
        result_from_service = service.get_enabled_tools()
    except Exception as service_error:
        error_msg = f"从服务获取工具列表失败: {str(service_error)},https://{gateway_domain}/mcpToolInfo?onlyData=true"
        logger.error(error_msg)
        try:
            send_feishu_alarm(error_msg)
        except Exception as alarm_error:
            logger.error(f"发送飞书告警失败: {str(alarm_error)}")
        result_from_service = []

    # 处理HTTP API获取的工具数据
    try:
        for tool_data in result:
            try:
                tool = {
                    "tool_id": tool_data["name"],  # 使用name作为tool_id
                    "tool_name": tool_data["name"],
                    "description": tool_data["description"],
                    "url": tool_data.get("url", ""),  # 使用get方法，如果url不存在则返回空字符串
                    "type": "system",  # 系统工具
                    "label": tool_data["name"],  # 默认标签为tool
                    "execution_mode": tool_data.get("executionMode", "sync"),  # 默认执行模式为sync
                    "parameters": tool_data.get("parameters", {}) or tool_data.get("inputSchema", {}).get("string",
                                                                                                          "{}"),
                }
                tools.append(tool)
            except Exception as tool_error:
                error_msg = f"处理工具数据失败: {str(tool_error)}, 工具数据: {str(tool_data)}"
                logger.error(error_msg)
                try:
                    send_feishu_alarm(error_msg)
                except Exception as alarm_error:
                    logger.error(f"发送飞书告警失败: {str(alarm_error)}")
                continue
    except Exception as iterate_error:
        error_msg = f"迭代工具列表失败: {str(iterate_error)}"
        logger.error(error_msg)
        try:
            send_feishu_alarm(error_msg)
        except Exception as alarm_error:
            logger.error(f"发送飞书告警失败: {str(alarm_error)}")

    try:
        send_feishu_alarm(f"from planning MCP工具列表: {json.dumps(tools, ensure_ascii=False, indent=2)}")
    except Exception as alarm_error:
        logger.error(f"发送飞书告警失败: {str(alarm_error)}")
    return tools


def extract_person_name(task_description: str, toc_user: Optional[ToCUser] = None, ) -> str:
    """
      从任务描述中提取人名，如果提供了toc_user则返回toc_user，否则尝试从任务描述中提取人名
      如果都无法获取，则返回默认值"汉俊"

      Args:
          task_description: 任务描述文本
          toc_user: 可选的用户名称，如果提供则优先使用

      Returns:
          str: 提取到的人名或默认值"汉俊"
      """
    # 常见的人名提取模式
    patterns = [
        r'分别发给\s*([^，。\n]+)',  # 分别发给 张三、李四、王五
        r'发给\s*([^，。\n]+)',  # 发给 张三、李四、王五
        r'由\s*([^，。\n]+)\s*负责',  # 由张三负责
        r'负责人[：:]\s*([^，。\n]+)',  # 负责人：张三
        r'([^，。\n]+)\s*执行',  # 张三执行
        r'([^，。\n]+)\s*负责',  # 张三负责
        r'([^，。\n]+)\s*处理',  # 张三处理
        r'([^，。\n]+)\s*填写',  # 张三填写
        r'([^，。\n]+)\s*发送',  # 张三发送
    ]

    for pattern in patterns:
        matches = re.findall(pattern, task_description)
        if matches:
            # 只取第一个匹配结果
            match = matches[0]
            # 分割可能的多个人名，只取第一个
            names = re.split(r'[、,，]', match.strip())
            if names:
                return names[0].strip()

        # toc_user，则优先使用
    if toc_user:
        return toc_user.name

        # 如果所有模式都匹配不到，返回默认值
    return "汉俊"


def validate_task_queryText(query: str, inputs: Mapping[str, Any], history_messages: list = [],
                            dataset_ids: list = [], toc_user: ToCUser = None, resource_contents: Dict[str, Any] = None) -> \
        Generator[Dict[str, Any], None, None]:
    """
    验证用户输入的任务描述是否完整，是否符合生成计划的要求

    Args:
        query: 用户输入的任务描述
        history_messages: 历史对话消息列表
        dataset_ids: 数据集ID列表

    Yields:
        str: 流式返回验证结果，如果任务描述完整返回"1"，如果不完整返回需要补充的内容
    """
    logger.info(f"入参query: {query}")
    logger.info(f"入参inputs: {inputs}")
    logger.info(f"入参history_messages: {history_messages}")
    logger.info(f"入参dataset_ids: {dataset_ids}")

    files, connections = extract_inputs(inputs)
    system_prompt = f"""
---

# Plan and Information Completeness Assessment Assistant

As a Plan and Information Completeness Assessment Assistant, I will evaluate the completeness of your input based on the information and reference materials you provide. My goal is to ensure all necessary details are covered for successful execution.

---

## Assessment Criteria

### Input Type Determination:

* **Plan**: If user input is a plan, I will assess its completeness using the "Elements of a Complete Plan" criteria.
* **Item/Query**: If user input is a specific item or query, I will judge its completeness based on "General Information Completeness" standards.
    * **Complete Example**: "Query tomorrow's weather in Hangzhou" – Includes time and location, thus complete.
    * **Incomplete Example**: "Query Hangzhou weather" – Lacks a date; I will ask you for which day you want the weather.

### Special Completion Plan Case
* If user supplement the plan over 10 rounds, the plan will be considered complete.
* If user input explicitly states that no further supplementation is needed, directly requests plan generation, accepts default options, or indicates indifference, I will consider the plan complete and approve it.

### Output Format:

All assessment results will strictly adhere to the "Assessment Result Format".

---

## Reference Material Handling Rules

I will process the "Reference Materials" according to the following rules:

* **Specific Requirements**: If the "Reference Materials" contain your specific requirements for the assessment criteria, I will prioritize these.
* **Supplementary Information**: If the "Reference Materials" provide additional content, I will merge them with your primary input before the assessment.
* **Both**: If "Reference Materials" contain both requirements and supplementary information, I will first integrate the information and then proceed with the assessment.

---

## Elements of a Complete Plan (Minimum Set)

A complete plan must contain at least the following core elements:

* **Clear Objective**: The objective must be Specific, Measurable, Achievable, Relevant, and Time-bound (SMART).
* **Resource Consideration**: The plan must estimate and account for required resources, such as time, human resources, finances, and materials.
* **Action Path**: The plan must outline any specific items, methods, steps, or strategies required to achieve the goals.

---

## Assessment Result Format

I will strictly output the assessment results in the following JSON format, ensuring the output language matches your input language.

**Key Notes:**

* **Guiding Questions**: If the plan is incomplete, I will use up to 5 guiding questions to prompt you for more information. These questions will focus on the major missing items or steps in the plan and provide relevant options, avoiding overly specific details.
* **Completeness Determination**:
    * If user supplement the plan over 10 rounds, the plan will be considered complete.
    * If user input explicitly states that no further supplementation is needed, directly requests plan generation, accepts default options, or indicates indifference, I will consider the plan complete and approve it.
* **Guided Supplementation**: I will proactively guide user to supplement missing core information, even if it doesn't affect plan details.
* **Third tool processing instructions**: No need to inquire about the third-party tools used by the user.
* **Person Task Deal Rule**: Do not ask users for information about human tasks in input and reference materials. Example 1: Determine the live streaming theme and format, and manually execute it. Example 2: Arrange Wang Shuang to prepare for the quarterly production and research meeting.
* **Rhetorical Question Example**:
    * Example 1: User - helps me plan a trip to Xizang. 
         **correct rhetorical question: May I ask where your starting point is? When are you planning to depart? Which mode of transportation would you like to use (such as airplane/train/autopilot)? What is your budget? What model of car are you planning to drive? 
         **incorrect rhetorical question: What model of car are you planning to drive? Is it necessary to separate the expenditure plans for sub projects such as transportation, catering, and tickets for the budget of 8000 yuan?
    * Example 2: User - Help me check the weather in Hangzhou. 
         **correct rhetorical question: Which day's weather do you want to check? (For example: Today/Tomorrow/The Day After Tomorrow) 
         **incorrect rhetorical question: What accuracy standard is required for weather query results? Do we need to simultaneously obtain composite data such as temperature, precipitation, and wind power?
    * Example 3: User - Use image tools to generate images of Confucius Temple. 
         **correct rhetorical question: Do you want to generate daytime or nighttime images?
         **incorrect rhetorical question: Regarding image generation: Do I need to specify parameters such as shooting angle/time period?

```json
{{{{
  "plan_integrity": "yes/no",  /* Indicates whether the plan is complete or the semantic completeness of user input */
  "plan_reason": "Reason for incompleteness",  /* Provided only when plan_integrity is "no" */
  "plan_name": "Generate a plan title based on user input, within 5-20 characters, with language consistent with user input"  /* Provided only when plan_integrity is "yes" */
  "scene": "Plan/Item/Query",  /* User input category */
  "scene_reason": "Reason for determining whether the user's input is Plan/Item/Query",
  "rhetorical_question": "When the plan is incomplete, use guiding questions to prompt users that core information is missing. Firstly, classify in a clear and friendly manner, then ask and refer to the 【rhetorical question example】 to ask the correct example, avoid incorrect examples, be careful not to ask too detailed questions, focus on the project and steps, and do not need relationships. Only return text format."  /* Provided only when plan_integrity is "no" */
}}}}
```

### **Reference Material**
* **related data (may be empty):
{json.dumps(resource_contents, ensure_ascii=False, indent=2)}

* **user files (may be empty):
{json.dumps(files, ensure_ascii=False, indent=2)}

* **database && table information (may be empty):
{json.dumps(connections, ensure_ascii=False, indent=2)}
"""
    logger.info(f"验证任务描述：{system_prompt}")
    # 构造对话历史
    messages = [{"role": "system", "content": system_prompt}]
    if history_messages:
        for msg in history_messages:
            messages.append({"role": "user", "content": msg.query})
            messages.append({"role": "assistant", "content": msg.answer})

    # 添加当前查询
    messages.append({"role": "user", "content": query})

    try:
        # 调用大模型进行验证，流式返回结果
        logger.info(f"验证任务描述：{query}")
        logger.info(f"历史消息：{history_messages}")
        logger.info(f"对话历史：{messages}")

        # 改为非流式调用
        full_response = ""
        for chunk in llm_client.call_qwen("", system_prompt, messages, stream=False):
            if not chunk:
                continue
            full_response += chunk
        logger.info(f"验证任务描述结果：{full_response}")

        # 解析返回的json
        try:
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            result_json = json.loads(full_response)
            if isinstance(result_json, dict) and result_json.get("plan_integrity") == "yes":
                plan_name = result_json.get("plan_name", "") if isinstance(result_json,
                                                                           dict) else ""
                rsp = {"is_valid": True, "missing_info": plan_name}
            else:
                # 容错处理
                plan_reason = result_json.get("plan_reason", "") if isinstance(result_json, dict) else ""
                if not isinstance(plan_reason, str):
                    plan_reason = str(plan_reason) if plan_reason is not None else ""
                rhetorical_question = result_json.get("rhetorical_question", "") if isinstance(result_json,
                                                                                               dict) else ""
                if not isinstance(rhetorical_question, str):
                    rhetorical_question = str(rhetorical_question) if rhetorical_question is not None else ""
                rsp = {
                    "is_valid": False,
                    "missing_info": rhetorical_question
                }
            yield rsp
        except Exception as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}")
            rsp = {"is_valid": False, "missing_info": "模型返回内容无法解析: " + str(e)}
            yield rsp

    except Exception as e:
        print(f"验证任务描述时发生错误: {str(e)}")
        raise Exception(f"验证任务描述时发生错误: {str(e)}")


def call_llm_analyze_parameters(
        root_task: TaskNode,
        atomic_tasks: List[TaskNode],
        task_map: Dict[str, TaskNode],
        available_files: Optional[List[Dict[str, str]]] = None,
        available_connections: Optional[List[Dict[str, str]]] = None,
        available_knowledge_bases: Optional[List[Dict[str, Any]]] = None,
        toc_user: Optional[ToCUser] = None,
        retrieve_datas: Optional[Dict[str, List[str]]] = None,
        conversation_id: Optional[str]=None
) -> Generator[str, None, None]:
    """
    调用大模型一次性分析所有原子任务的工具选择和参数配置，并分配相关文件

    Args:
        atomic_tasks: 需要分析参数的原子任务列表
        task_map: 任务ID映射表
        available_files: 可用的文件列表，每个文件包含：
            - file_id: 文件ID
            - filename: 文件名
            - type: 文件类型
            - transfer_method: 传输方式
            - url: 文件URL（如果有）
            - available_connections: 可用的连接列表，每个连接包含：
            - available_knowledge_bases: 可用的知识库列表：
            - type: 连接类型
            - connection_id: 连接ID
            - table_id: 表ID
            - table: 表名
        toc_user: 可选的用户名称，用于提取人名
        retrieve_datas: 检索信息

    Yields:
        str: 流式输出的内容片段
    """
    if retrieve_datas is None:
        retrieve_datas = {}
    logger.info("[PLAN_GEN] 开始分析工具参数")
    logger.info(f"[PLAN_GEN] 待分析原子任务: {[task.task_name for task in atomic_tasks]}")
    logger.info(f"[PLAN_GEN] 可用文件: {json.dumps(available_files, ensure_ascii=False, indent=2)}")
    logger.info(f"[PLAN_GEN] 可用数据库连接: {json.dumps(available_connections, ensure_ascii=False, indent=2)}")

    reference_information = ""
    if retrieve_datas:
        for source_name, items in retrieve_datas.items():
            reference_information += f"{source_name}:\n"
            for item in items:
                reference_information += f"{item}\n"

    system_prompt = """You are an expert task parameter analyzer and tool selector. CRITICAL REQUIREMENT: You must return a perfectly formatted JSON object with no errors.

STRICT JSON FORMATTING RULES:
1. Your entire response must be a single, valid JSON object
2. No markdown code blocks, explanations, or other text outside the JSON
3. No trailing commas in objects or arrays
4. All property names must be in double quotes
5. Properly escape all special characters in strings
6. All arrays and objects must be properly closed
7. Boolean values must be lowercase (true/false)
8. Check your response carefully before returning

TASK ANALYSIS REQUIREMENTS:
1. Human Tasks:
   - For tasks containing "人工执行", "手动执行", "manual task" or requiring human execution, you MUST assign an async tool
   - Tasks requiring human judgment, physical actions, or communication need async tools
   - When assigning a human task, executor field must contain the appropriate person based on role information
   - Manual tasks need to be assigned to specific people, not roles

2. CRITICAL DISTINCTION BETWEEN REFERENCE AND EXECUTION DEPENDENCIES:
   a) Reference Information (DO NOT include in task dependencies):
      - Knowledge bases containing task definitions or plan outlines
      - Files that only describe what tasks need to be done
      - Resources used only for understanding and planning the task
      - Information that provides guidance but isn't processed directly
      - Documents explaining the workflow structure
      - EXTREMELY IMPORTANT: Knowledge bases, files, or databases that only contain descriptive information about tasks SHOULD NOT be included as dependencies

   b) Execution Dependencies (ONLY these should be included):
      - Files that the task directly processes or transforms during execution
      - Knowledge bases that the task actually queries or updates
      - Database connections that the task reads from or writes to
      - Resources that the task's tool directly manipulates
      - Files specifically mentioned in the task description as being used
      - EXTREMELY IMPORTANT: Only resources that the task actually uses IN ITS OPERATION

3. Response Structure:
   - Each task_id must be a key in the root object
   - Every task must have all required fields, even if empty arrays/objects
   - Follow the exact format shown in the RETURN FORMAT section
"""

    available_tools = McpTools.retrieve_tools_for_task(root_task.task_description)
    # 构建提示词
    prompt = f"""
Task Parameter Analysis Request:

AVAILABLE TOOLS:
{chr(10).join([
        f"Tool:" + chr(10) +
        f"  ID: {tool['tool_id']}" + chr(10) +
        f"  Name: {tool['tool_name']}" + chr(10) +
        f"  Description: {tool['description']}" + chr(10) +
        f"  Parameters: {tool.get('parameters', {})}" + chr(10) +
        f"  Execution Mode: {tool.get('execution_mode', 'sync')} (sync=machine, async=human)"
        for tool in available_tools
    ])}

TASKS TO ANALYZE:
{chr(10).join([
        f"Task:" + chr(10) +
        f"  ID: {task.task_id}" + chr(10) +
        f"  Name: {task.task_name}" + chr(10) +
        f"  Description: {task.task_description}" + chr(10) +
        f"  Expected Output: {task.expected_deliverables}"
        for task in atomic_tasks
    ])}

AVAILABLE RESOURCES:
Files (注意：以下文件可能包含描述任务的参考资料，不要将参考资料作为执行依赖):
{chr(10).join([
        f"File: {file.get('filename', '')}, ID: {file.get('file_id', '')}, Type: {file.get('type', '')}"
        for file in (available_files or [])
    ])}

Database Connections:
{chr(10).join([
        f"Table: {connection.get('table', '')}"
        for connection in (available_connections or [])
    ])}

KnowledgeBase (注意：以下知识库可能包含描述任务的参考资料，不要将参考资料作为执行依赖):
{chr(10).join([
        f"Knowledge: {knowledge_base.get('name', '')}, ID: {knowledge_base.get('id', '')}"
        for knowledge_base in (available_knowledge_bases or [])
    ])}

REFERENCE INFORMATION:
{reference_information}

TASK ANALYSIS INSTRUCTIONS:
1. Human vs Automated Tasks:
   - CRITICALLY IMPORTANT: Tasks containing "人工执行", "手动执行", "manual task" MUST have an async tool assigned
   - For roles mentioned in REFERENCE INFORMATION (like "物料专员", "技术支持"), identify the corresponding person
   - Only leave tool_id empty for tasks that genuinely don't need tools AND are not human tasks

2. Resource Allocation (EXTREMELY IMPORTANT):
   a) Start with the assumption that each task needs NO resources
   b) How to identify execution dependencies (DO include these):
      - Resources the task will directly manipulate or transform
      - Files that the task directly processes or uses as input
      - Data sources that the task needs to query or modify
      - Only resources that are ACTUALLY NEEDED during execution
      
   c) How to identify reference materials (DO NOT include these):
      - Files or knowledge bases that just describe what the task should do
      - Documentation about the workflow structure
      - Plan outlines or task definitions
      - ANY resource that only provides information about tasks but isn't directly manipulated

   d) Examples to clarify:
      - A knowledge base containing task instructions is a REFERENCE (don't include)
      - A file containing data that must be processed is an EXECUTION DEPENDENCY (include)
      - A document describing workflow steps is a REFERENCE (don't include)
      - A database that must be queried during the task is an EXECUTION DEPENDENCY (include)

3. Person Assignment:
   - For human tasks, carefully find the appropriate person from REFERENCE INFORMATION
   - Match roles with specific people whenever possible
   - Must be a specific person's name (e.g. "张伟")
   - Default to {toc_user.name if toc_user else "默认执行人"} only when no specific person can be identified

RETURN FORMAT - EXACT STRUCTURE REQUIRED:
{{
  "task_id_1": {{
    "selected_tool_id": "tool_id",
    "parameters": {{}},
    "executor": "person_name",
    "required_file_ids": [],
    "required_tables": [],
    "required_knowledge_base": []
  }},
  "task_id_2": {{
    // Next task...
  }}
}}

IMPORTANT JSON VALIDATION CHECKLIST:
- Verify all JSON syntax is valid
- Ensure all opening brackets have matching closing brackets
- Check that all property names are in double quotes
- Remove any trailing commas
- Make sure all task IDs are included
- Confirm human tasks have async tools assigned
- DOUBLE-CHECK that you haven't included reference materials as execution dependencies

YOU MUST RETURN ONLY A VALID JSON OBJECT. NO MARKDOWN, NO EXPLANATIONS, JUST THE JSON.
"""

    logger.info("[PLAN_GEN] 开始调用大模型分析工具参数 系统提示词:")
    logger.info(system_prompt)
    logger.info("[PLAN_GEN] 开始调用大模型分析工具参数 用户提示词:")
    logger.info(prompt)

    try:
        # 调用大模型获取工具选择和参数分析结果
        full_response = ""
        root_task.log += f"正在分析工具选择和参数：{[task.task_name for task in atomic_tasks]}\n"
        yield root_task.to_dict()

        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"[PLAN_GEN] 开始调用大模型分析工具参数 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

        for chunk in llm_client.call_deepseek(prompt=prompt, system_prompt=system_prompt, toc_user=toc_user, conversation_id=conversation_id):
            full_response += chunk

        # 记录结束时间并计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[PLAN_GEN] 大模型分析工具参数完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"[PLAN_GEN] 生成PLAN耗时统计：大模型分析工具参数耗时: {duration:.2f}秒")

        root_task.log += f"任务:{[task.task_name for task in atomic_tasks]} 工具选择和参数分析完成\n"
        yield root_task.to_dict()

        logger.info("[PLAN_GEN] 大模型返回结果:")
        logger.info(full_response)

        # 清理并解析JSON
        cleaned_response = full_response.replace('```json', '').replace('```', '').strip()
        try:
            analysis = json.loads(cleaned_response)
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}, 原始响应: {cleaned_response}")
            raise Exception(f"解析大模型返回的JSON失败: {str(e)}")
        
        logger.info("[PLAN_GEN] 解析后的工具参数分析结果:")
        logger.info(json.dumps(analysis, ensure_ascii=False, indent=2))

        if not isinstance(analysis, dict):
            logger.error(f"大模型返回的分析结果格式错误: {full_response}")
            return

        # 处理每个任务的工具选择结果
        for task in atomic_tasks:
            task_analysis = analysis.get(task.task_id, {})
            selected_tool_id = task_analysis.get("selected_tool_id")
            parameters = task_analysis.get("parameters", {})
            required_file_ids = task_analysis.get("required_file_ids", [])
            required_tables = task_analysis.get("required_tables", [])
            executor = task_analysis.get("executor")

            required_knowledge_base_ids = task_analysis.get("required_knowledge_base", [])

            logger.info(f"selected_tool_id is: {selected_tool_id}")

            task.execution_mode = ExecutionMode.SYNC

            if not selected_tool_id:
                task.tools = []
                logger.warning(f"任务 {task.task_id} 未选择工具")
                continue

            # 找到选中的工具并创建深拷贝
            selected_tool = next(
                (copy.deepcopy(tool) for tool in available_tools if tool["tool_id"] == selected_tool_id), None)
            if not selected_tool:
                task.tools = []
                logger.warning(f"任务 {task.task_id} 选择的工具 {selected_tool_id} 不存在")
                continue

            # 如果是异步工具，设置执行者
            if selected_tool.get("execution_mode") == "async":
                # 优先使用大模型选择的执行者，如果没有则使用extract_person_name的结果
                if executor:
                    selected_tool["tool_name"] = executor
                    selected_tool["tool_id"] = executor  # 设置tool_id等于tool_name
                    logger.info(f"任务 {task.task_id} 的异步工具已设置执行人: {executor}")
                else:
                    person_name = extract_person_name(task.task_description, toc_user)
                    selected_tool["tool_name"] = person_name
                    selected_tool["tool_id"] = person_name  # 设置tool_id等于tool_name
                    logger.info(f"任务 {task.task_id} 的异步工具已设置执行人: {person_name}")

            # 设置参数
            selected_tool["parameters"] = parameters

            # 设置任务需要的文件，从可用文件中筛选
            task.files = []
            for file_id in required_file_ids:
                matching_file = next(
                    (f for f in available_files if f.get('file_id') == file_id),
                    None
                )
                if matching_file:
                    task.files.append(matching_file)

            # 设置任务需要的表，从可用表中筛选
            task.connections = []
            for table in required_tables:
                matching_table = next(
                    (t for t in available_connections if t.get('table') == table),
                    None
                )
                if matching_table:
                    task.connections.append(matching_table)

            # 设置任务需要的知识库，从可用知识库中筛选
            task.knowledge_base = []
            for dataset_id in required_knowledge_base_ids:
                matching_knowledge_base = next(
                    (f for f in available_knowledge_bases if f.get('id') == dataset_id),
                    None
                )
                if matching_knowledge_base:
                    task.knowledge_base.append(matching_knowledge_base)

            selected_tool["files"] = task.files
            selected_tool["connections"] = task.connections
            # 更新任务的工具列表，只保留选中的工具
            task.tools = [selected_tool]

            # 设置任务的执行模式
            task.execution_mode = ExecutionMode.ASYNC if selected_tool.get(
                "execution_mode") == "async" else ExecutionMode.SYNC
            logger.info(f"任务 {task.task_id} 设置为 {task.execution_mode.value} 执行模式")

        yield ""
    except json.JSONDecodeError as e:
        logger.error(f"解析大模型返回的JSON失败: {str(e)}")
        logger.error(f"大模型返回的分析结果: {full_response}")

        raise Exception(f"解析大模型返回的JSON失败: {str(e)}")
    except Exception as e:
        logger.error(f"分析工具参数时发生错误: {str(e)}")
        raise e

def generate_schedule_for_task(
        task: TaskNode,
        toc_user: Optional[ToCUser] = None,
        conversation_id: Optional[str] = None
) -> Generator[str, None, None]:
    """
    使用大模型解析任务描述中的调度信息，并设置到任务节点中，支持流式输出

    Args:
        task: 任务节点

    Yields:
        str: 流式输出的内容片段
    """
    # 获取当前时间
    current_time = datetime.now(timezone.utc)
    current_time_str = current_time.isoformat()

    system_prompt = """You are an expert task scheduler with deep knowledge of workflow automation and scheduling patterns. Your role is to:

1. Analyze task descriptions for scheduling requirements.
2. Identify recurring patterns and timing constraints.
3. Generate appropriate **standard 5-field cron expressions (minute, hour, day of month, month, day of week)** that are strictly parsable by the `croniter` library. For example, "every day at 8 AM" should become "0 8 * * *".
4. Determine optimal start times if specified.
5. Return responses in valid JSON format only.

Key Responsibilities:
- Extract scheduling patterns from task descriptions.
- Convert natural language timing to **`croniter`-compatible 5-field cron expressions**.
- Ensure cron expressions use standard syntax:
    - Minute (0-59)
    - Hour (0-23)
    - Day of Month (1-31)
    - Month (1-12)
    - Day of Week (0-6 or 0-7, where 0 or 7 is Sunday, or use names like SUN, MON) - **prefer numerical values (0-6 for Sunday-Saturday) for maximum `croniter` compatibility.**
    - Special characters: `*` (any), `*/n` (every n), `,` (list separator), `-` (range).
- Consider timezone implications (though the cron expression itself is timezone-agnostic, the interpretation relies on the execution environment's timezone awareness).
- Validate scheduling configurations for logical consistency.
- Optimize for efficient execution.
"""

    prompt = f"""
Task Scheduling Analysis Request:

CURRENT TIME: {current_time_str}
TASK INFORMATION:
- Name: {task.task_name}

Please analyze the task description for scheduling requirements and return a JSON object according to the following rules:

1. If NO scheduling information is found in the task description:
   Return an empty object: {{}}

2. If scheduling information is found:
   - If only a schedule interval is found:
     Return: {{"schedule_interval": "A_VALID_5_FIELD_CRONITER_COMPATIBLE_CRON_EXPRESSION"}}
   - If both a schedule interval and a specific start time are found:
     Return: {{"schedule_interval": "A_VALID_5_FIELD_CRONITER_COMPATIBLE_CRON_EXPRESSION", "start_time": "ISO_8601_TIMESTAMP"}}

**CRON Expression Generation Guidelines (for `croniter` compatibility):**
- The cron expression MUST be a string with 5 space-separated fields: Minute, Hour, Day-of-Month, Month, Day-of-Week.
- Day-of-Week: Use 0 for Sunday, 1 for Monday, ..., 6 for Saturday.

**start_time Generation Guidelines:**
- The `start_time` MUST be a valid ISO 8601 timestamp, including date and time (e.g., `"2025-06-18T08:00:00+00:00"` or `"2025-06-18T08:00:00"`).
- If the task description specifies only a time (e.g., "every day at 8 AM"), use the time from the description and the date from `current_time_str`. For example:
  - Task: "every day at 8 AM", Current Time: `2025-06-15T14:30:00+00:00`
  - Generate: `"start_time": "2025-06-15T08:00:00"`
- If no date is specified, assume the task starts on the date of `current_time_str`.
- If no time is specified, default to `00:00:00` for the time portion.
- **Examples:**
  - Task: "every day at 8 AM", Current Time: "2025-06-18T15:20:00+09:00" -> `"start_time": "2025-06-18T08:00:00"`
  - Task: "every Monday at 14:30", Current Time: "2025-06-15T10:00:00+00:00" -> `"start_time": "2025-06-16T14:30:00"` (next Monday’s date)
  - Task: "on July 4, 2024, at 12:00 PM" -> `"start_time": "2024-07-04T12:00:00"`

Common Scheduling Patterns and **Target Cron Examples**:
1. Daily Patterns:
   - "每天早上8点" (every day at 8 AM) -> `"0 8 * * *"`
   - "每日下午3:30" (every day at 3:30 PM) -> `"30 15 * * *"`
   - "at 10 o'clock every day" -> `"0 10 * * *"`

2. Weekly Patterns:
   - "每周一上午9点" (every Monday at 9 AM) -> `"0 9 * * 1"`
   - "每周三和周五晚上7点" (every Wednesday and Friday at 7 PM) -> `"0 19 * * 3,5"`
   - "每周工作日早上10点" (every weekday at 10 AM) -> `"0 10 * * 1-5"`
   - "每周末凌晨2点" (every weekend at 2 AM) -> `"0 2 * * 0,6"` (assuming Sunday is 0, Saturday is 6)

3. Monthly Patterns:
   - "每月1号凌晨3点15分" (on the 1st of every month at 3:15 AM) -> `"15 3 1 * *"`
   - "每月15日中午12点" (on the 15th of every month at 12 PM) -> `"0 12 15 * *"`
   - Note: Phrases like "月初" (early month), "月中" (mid-month), "月末" (end of month) are harder to convert to a single standard cron. If specific day is not mentioned, try to extract it or it might be unschedulable via cron. If it's like "每月第一个周一" (first Monday of the month), this requires more complex cron features that `croniter` might support (e.g. `0 0 * * 1#1`) but stick to basic expressions if possible or if the feature is not standard. For simplicity, initially focus on specific day numbers.

4. Hourly Patterns:
   - "每小时的30分" (every hour at 30 minutes past the hour) -> `"30 * * * *"`
   - "每2小时执行一次，从0点开始" (every 2 hours starting at 00:00) -> `"0 */2 * * *"` (at minute 0, every 2nd hour)
   - "每隔3小时的15分" (every 3 hours at 15 minutes past the hour) -> `"15 */3 * * *"`

5. Custom Patterns:
   - For specific dates like "2024年7月4日下午2点", this is a one-time execution, not a recurring schedule. The primary goal here is recurring `schedule_interval`. If a specific one-time run is implied without recurrence, `schedule_interval` might not be appropriate, or it implies a start_time for a recurring job that starts then.

Please return only valid JSON without any additional text or explanations.
Ensure the `schedule_interval` is ALWAYS a `croniter`-compatible 5-field cron string.
"""

    try:
        full_response = ""
        task.log += f"正在分析任务调度信息：{task.task_name}\n"
        yield task.to_dict()
        for chunk in llm_client.call_deepseek(prompt=prompt, system_prompt=system_prompt,  toc_user=toc_user, conversation_id=conversation_id):
            full_response += chunk
            # if "json" in chunk or "`" in chunk:
            #     continue
            # task.log += chunk
            # yield task.to_dict()  # 将每个片段传递给调用者
        task.log += f"任务:{task.task_name} 调度信息解析中\n"
        yield task.to_dict()

        # 清理响应内容，移除可能的markdown代码块标记
        full_response = full_response.replace('```json', '').replace('```', '').strip()
        logger.info(f"Schedule analysis response: {full_response}")
        schedule_info = json.loads(full_response)

        # 处理调度信息
        if not schedule_info:
            logger.info(f"任务 {task.task_name} 没有调度信息")
            task.log += "未找到调度信息\n"
            yield task.to_dict()
            return

        # 设置调度间隔
        if "schedule_interval" in schedule_info:
            schedule_interval = schedule_info["schedule_interval"]
            if schedule_interval:
                task.schedule_interval = schedule_interval
                logger.info(f"设置任务调度间隔: {schedule_interval}")
                task.log += f"设置调度间隔: {schedule_interval}\n"

        # 设置开始时间
        if "start_time" in schedule_info:
            start_time = schedule_info["start_time"]
            if start_time:
                try:
                    task.start_time = datetime.fromisoformat(start_time)
                    logger.info(f"设置任务开始时间: {start_time}")
                    task.log += f"设置开始时间: {start_time}\n"
                except ValueError as e:
                    logger.error(f"解析开始时间失败: {str(e)}")
                    task.log += f"解析开始时间失败: {str(e)}\n"

        task.log += f"任务:{task.task_name} 调度信息解析完成\n"
        yield task.to_dict()
    except Exception as e:
        logger.error(f"分析调度信息时发生错误: {str(e)}")
        logger.error(f"错误详情: {str(e)}")
        raise Exception(f"分析调度信息时发生错误: {str(e)}")


class ComplexPlanningBaseGenerator:
    def __init__(self, conversation_id: str):
        self.root_task = None
        self.conversation_id = conversation_id
        self.task_map = {}
        self.retrieve_datas = {}
        self._retry_counters = {}  # 用于跟踪各个生成函数的重试次数

    def _get_retry_key(self, func_name: str, task_id: str) -> str:
        """生成重试计数器的键"""
        return f"{func_name}_{task_id}"

    def _increment_retry_counter(self, func_name: str, task_id: str) -> int:
        """增加重试计数并返回当前重试次数"""
        key = self._get_retry_key(func_name, task_id)
        self._retry_counters[key] = self._retry_counters.get(key, 0) + 1
        return self._retry_counters[key]

    def get_knowledge_base_content(self) -> str:
        return ""

    def preprocess_knowledge_base(self, query, dataset_ids, search_data, history_messages):
        pass

    def call_llm_analyze_dependencies(
            self,
            subtasks: List[TaskNode],
            task_map: Dict[str, TaskNode],
            retrieve_datas: Optional[Dict[str, List[str]]] = None,
            toc_user: Optional[ToCUser] = None,
    ) -> Generator[str, None, Dict[str, Dict[str, List[str]]]]:
        """
        调用大模型一次性分析所有子任务之间的依赖关系，支持流式输出

        Args:
            subtasks: 需要分析依赖关系的子任务列表
            task_map: 任务ID映射表，包含所有任务的信息
            retrieve_datas: 检索到的数据字典，key为数据源名，value为内容列表
            toc_user: 可选的ToCUser对象，用于提取人名

        Yields:
            str: 流式输出的内容片段
        Returns:
            Dict[str, Dict[str, List[str]]]: 每个任务ID对应的依赖关系，包含sibling_dependencies和data_dependencies
        """
        raise NotImplementedError("Subclasses must implement call_llm_analyze_dependencies")

    def analyze_new_dependencies(self, task_node: TaskNode, task_map: Dict[str, TaskNode]) -> None:
        """分析新增节点的依赖关系"""
        # 构建任务上下文
        task_context = []
        current = task_node
        while current.parent_id:
            parent = task_map.get(current.parent_id)
            if parent:
                task_context.append({
                    "task_id": parent.task_id,
                    "task_name": parent.task_name,
                    "task_description": parent.task_description,
                    "expected_deliverables": parent.expected_deliverables
                })
                current = parent
            else:
                break

        # 获取同级任务
        parent_id = task_node.parent_id
        siblings = []
        if parent_id:
            siblings = [child for child in task_map.values()
                        if child.parent_id == parent_id and child.task_id != task_node.task_id]

        # 构建任务节点列表，包含当前任务和其同级任务
        tasks_to_analyze = [task_node] + siblings

        if parent_id:
            task_map[parent_id].children = tasks_to_analyze

        # 调用大模型分析依赖关系
        full_response = ""
        for chunk in self.call_llm_analyze_dependencies(tasks_to_analyze, task_map):
            full_response += chunk

        # 依赖关系已经在call_llm_analyze_dependencies中处理完成

    def edit_task_tree(
            self,
            original_task_tree: Dict[str, Any],
            modified_task_tree: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        比较并编辑任务树，找出新增和删除的节点，并解绑相关依赖关系

        Args:
            original_task_tree: 原始任务树的JSON数据
            modified_task_tree: 修改后的任务树的JSON数据

        Returns:
            Dict[str, Any]: 修改后的任务树，已清理相关依赖关系
        """

        def collect_task_ids(task_node: Dict[str, Any]) -> Set[str]:
            """收集任务树中所有的任务ID"""
            task_ids = {task_node["task_id"]}
            for child in task_node.get("children", []):
                task_ids.update(collect_task_ids(child))
            return task_ids

        def remove_dependencies(task_node: Dict[str, Any], removed_ids: Set[str]) -> None:
            """递归移除任务节点中对已删除节点的依赖"""
            # 移除sibling_dependencies中的已删除节点
            if "sibling_dependencies" in task_node:
                task_node["sibling_dependencies"] = [
                    dep_id for dep_id in task_node["sibling_dependencies"]
                    if dep_id not in removed_ids
                ]

            # 移除data_dependencies中的已删除节点
            if "data_dependencies" in task_node:
                task_node["data_dependencies"] = [
                    dep_id for dep_id in task_node["data_dependencies"]
                    if dep_id not in removed_ids
                ]

            # 递归处理子节点
            for child in task_node.get("children", []):
                remove_dependencies(child, removed_ids)

        # 收集原始和修改后的任务ID集合
        original_task_ids = collect_task_ids(original_task_tree)
        modified_task_ids = collect_task_ids(modified_task_tree)

        # 找出新增和删除的任务ID
        added_task_ids = list(modified_task_ids - original_task_ids)
        removed_task_ids = list(original_task_ids - modified_task_ids)

        # 解绑已删除节点的依赖关系
        if removed_task_ids:
            remove_dependencies(modified_task_tree, set(removed_task_ids))

        # 处理新增节点
        if added_task_ids:
            # 构建任务映射表
            task_map = {}
            root_task = TaskNode.from_dict(modified_task_tree)

            def build_task_map(node: TaskNode) -> None:
                # 将字典转换为TaskNode对象
                task_map[node.task_id] = node
                for child in node.children:
                    build_task_map(child)

            build_task_map(root_task)

            # 分析新增节点的依赖关系
            for task_id in added_task_ids:
                task_node = task_map.get(task_id)
                if task_node:
                    self.analyze_new_dependencies(task_node, task_map)

            modified_task_tree = root_task.to_dict()

        return modified_task_tree

    def call_llm_analyze_supervisor(
            self,
            root_task: TaskNode,
            tasks: List[TaskNode],
            task_map: Dict[str, TaskNode],
            retrieve_datas: Optional[Dict[str, List[str]]] = None,
            toc_user: Optional[ToCUser] = None
    ) -> Generator[str, None, None]:
        """
        调用大模型分析任务的监督人信息

        Args:
            root_task: 根任务节点
            tasks: 需要分析监督人的任务列表
            task_map: 任务ID映射表
            retrieve_datas: 检索到的数据字典，key为数据源名，value为内容列表
            toc_user: 可选的ToCUser对象，用于提取人名

        Yields:
            str: 流式输出的内容片段
        """
        if retrieve_datas is None:
            retrieve_datas = {}
        logger.info("[PLAN_GEN] 开始分析任务监督人信息")
        logger.info(f"[PLAN_GEN] 待分析任务: {[task.task_name for task in tasks]}")

        system_prompt = """You are an expert task supervisor analyzer with deep knowledge of project management and team organization. Your role is to:

    1. Analyze tasks to determine if they need supervision
    2. Identify appropriate supervisors based on task requirements
    3. Consider task complexity, importance, and dependencies
    4. Return responses in valid JSON format only

    CRITICAL RULES FOR SUPERVISOR ASSIGNMENT:
    1. Supervisors are people who oversee, review, or approve the task, and must NOT be the same as executors unless the REFERENCE INFORMATION explicitly requires the same person.
    2. Only assign a supervisor if there is a clear indication (such as "审核", "审批", "监督", "验收", "评估", "检查", "确认", "把关", "上级", "负责人", etc.) in the reference information or task description.
    3. If there is no explicit supervision requirement, set `needs_supervisor` to `false` and return an empty supervisor object (`{}`).
    4. Never assign the executor as the supervisor unless it is explicitly required.
    5. The `supervisor.name` field must be a specific person's name (e.g., "张伟", "John Smith") and MUST NOT be a role (e.g., "项目经理", "负责人"), a vague term (e.g., "某某人"), or a placeholder.
    6. Validate that `supervisor.name` is a valid human name (e.g., contains Chinese characters, alphabetic characters, or a combination, and does not match patterns like "经理", "负责人", or "某某").
    7. When extracting supervisor information from reference data:
       - Prioritize fields explicitly labeled as person names (e.g., "审核人", "负责人姓名").
       - If only a role is provided (e.g., "项目经理"), map it to a specific person using a personnel list or context if available; otherwise, do NOT assign a supervisor.
       - If no specific person can be identified, return an empty supervisor object (`{}`).

    Key Responsibilities:
    - Evaluate task supervision needs
    - Match supervisors to task requirements
    - Consider task context and dependencies
    - Validate supervisor assignments
    - Optimize for effective oversight
    """

        reference_information = ""
        if retrieve_datas:
            for source_name, items in retrieve_datas.items():
                reference_information += f"{source_name}:\n"
                for item in items:
                    reference_information += f"{item}\n"

        prompt = f"""
    Task Supervisor Analysis Request:

    TASKS TO ANALYZE:
    {chr(10).join([
            f"Task {i + 1}:" + chr(10) +
            f"  ID: {task.task_id}" + chr(10) +
            f"  Name: {task.task_name}" + chr(10) +
            f"  Description: {task.task_description}" + chr(10) +
            f"  Expected Output: {task.expected_deliverables}" + chr(10) +
            f"  Is Atomic: {task.is_atomic}" + chr(10) +
            f"  Execution Mode: {task.execution_mode.value if task.execution_mode else 'None'}" + chr(10) +
            f"  Tools: {json.dumps(task.tools, ensure_ascii=False) if task.tools else []}" + chr(10) +
            f"  Parent ID: {task.parent_id}" + chr(10)
            for i, task in enumerate(tasks)
        ])}

    TASK HIERARCHY:
    {chr(10).join([
            f"Parent Task:" + chr(10) +
            f"  ID: {task_map[task.parent_id].task_id}" + chr(10) +
            f"  Name: {task_map[task.parent_id].task_name}" + chr(10) +
            f"  Description: {task_map[task.parent_id].task_description}" + chr(10) +
            f"  Expected Output: {task_map[task.parent_id].expected_deliverables}" + chr(10)
            for task in tasks
            if task.parent_id and task.parent_id in task_map
        ])}

    REFERENCE INFORMATION:
    {reference_information}

    Please analyze each task and return a JSON object where:
    - Keys are task IDs
    - Values are objects containing:
      {{
        "needs_supervisor": boolean,  // Whether the task needs supervision
        "supervisor": {{
          "name": "supervisor_name",  // Specific person's name (e.g., "张伟"), or empty string ("") if no supervisor is assigned
          "type": "human",  // Type of supervisor (currently only "human" is supported)
          "id": "",  // ID for tool or agent type supervisor (empty for human type)
        }}
      }}
    Please return only valid JSON without any additional text or explanations.
    """

        # logger.info("[PLAN_GEN] 系统提示词:")
        # logger.info(system_prompt)
        # logger.info("[PLAN_GEN] 用户提示词:")
        # logger.info(prompt)

        try:
            # 调用大模型获取监督人分析结果
            full_response = ""
            root_task.log += f"正在分析任务监督人信息：{[task.task_name for task in tasks]}\n"
            yield root_task.to_dict()

            # 记录开始时间
            start_time = datetime.now()
            logger.info(f"[PLAN_GEN] 开始调用大模型分析监督人信息 - {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")

            for chunk in llm_client.call_deepseek(prompt=prompt, system_prompt=system_prompt, toc_user=toc_user, conversation_id=self.conversation_id):
                full_response += chunk

            # 记录结束时间并计算耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"[PLAN_GEN] 大模型分析监督人信息完成 - {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
            logger.info(f"[PLAN_GEN] 生成PLAN耗时统计：大模型分析监督人信息耗时: {duration:.2f}秒")

            root_task.log += f"任务:{[task.task_name for task in tasks]} 监督人信息分析完成\n"
            yield root_task.to_dict()

            logger.info("[PLAN_GEN] 大模型返回结果:")
            logger.info(full_response)

            # 处理完整的响应
            full_response = full_response.replace('```json', '').replace('```', '').strip()
            analysis = json.loads(full_response)
            logger.info("[PLAN_GEN] 解析后的监督人分析结果:")
            logger.info(json.dumps(analysis, ensure_ascii=False, indent=2))

            if not isinstance(analysis, dict):
                logger.error(f"大模型返回的分析结果格式错误: {full_response}")
                return

            # 处理每个任务的监督人信息
            for task in tasks:
                task_analysis = analysis.get(task.task_id, {})
                needs_supervisor = task_analysis.get("needs_supervisor", False)
                supervisor_info = task_analysis.get("supervisor", {})

                if needs_supervisor and supervisor_info:
                    task.supervisor = {
                        "name": supervisor_info.get("name", ""),
                        "type": supervisor_info.get("type", "human"),  # 默认为human类型
                        "id": supervisor_info.get("id", ""),  # 默认为空字符串
                    }
                    logger.info(f"任务 {task.task_id} 已设置监督人: {task.supervisor}")
                else:
                    task.supervisor = {}
                    logger.info(f"任务 {task.task_id} 不需要监督人")

            yield ""
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {str(e)}")
            logger.error(f"大模型返回的内容: {full_response}")
            raise Exception(f"解析大模型返回的JSON失败: {str(e)}")
        except Exception as e:
            logger.error(f"分析监督人信息时发生错误: {str(e)}")
            raise e


def retrieve_tools_by_ids(tool_ids: List[str]) -> List[Dict[str, str]]:
    """
    根据工具ID列表获取匹配的工具信息

    Args:
        tool_ids: 工具ID列表

    Returns:
        List[Dict[str, str]]: 匹配的工具列表，每个工具包含tool_id, name, description等信息

    Raises:
        Exception: 当API请求失败时抛出异常
    """
    # 如果tool_ids为空，直接返回空列表
    if not tool_ids:
        return []

    tools = []

    try:
        # 从Apollo配置中心获取域名
        try:
            gateway_domain = apollo_client.get_value(key="TASK_GATEWAY2_DOMAIN")
            # 检验域名是否有效
            if not gateway_domain or gateway_domain.lower() == "none":
                logger.warning("从Apollo获取到无效域名，使用默认值")
                gateway_domain = "test-gateway.ywwl.com/mcp-proxy"
        except Exception as apollo_error:
            logger.warning(f"从Apollo获取域名失败，使用默认值: {str(apollo_error)}")
            gateway_domain = "test-gateway.ywwl.com/mcp-proxy"

        # 调用mcpToolInfo接口获取所有工具
        logger.info(f"请求的URL: https://{gateway_domain}/mcpToolInfo?onlyData=true")
        response = requests.get(f"https://{gateway_domain}/mcpToolInfo?onlyData=true")

        if response.status_code == 200:
            response_data = response.json()
            # 兼容不同的响应结构
            if isinstance(response_data, dict) and 'data' in response_data:
                result = response_data.get('data', [])
            else:
                result = response_data

            # 确保result不为None
            if result is None:
                logger.error("获取工具列表返回结果为None")
                return []

            # 确保result是列表类型
            if not isinstance(result, list):
                logger.error(f"MCP工具列表不是一个列表，实际类型: {type(result)}")
                return []

            # 处理工具数据，只保留匹配的工具ID
            for tool_data in result:
                try:
                    tool_name = tool_data.get("name")
                    # 检查工具ID是否在请求的列表中
                    if tool_name in tool_ids:
                        tool = {
                            "tool_id": tool_name,  # 使用name作为tool_id
                            "tool_name": tool_name,
                            "description": tool_data.get("description", ""),
                            "parameters": tool_data.get("parameters", {}) or tool_data.get("inputSchema", {}).get(
                                "string", "{}")
                        }
                        tools.append(tool)
                except Exception as tool_error:
                    logger.error(f"处理工具数据失败: {str(tool_error)}, 工具数据: {str(tool_data)}")
                    continue
        else:
            logger.error(f"获取工具列表失败: {response.status_code}, {response.text}")
            return []

    except Exception as e:
        logger.error(f"获取工具列表失败: {str(e)}")
        return []

    return tools
