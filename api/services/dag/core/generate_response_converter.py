import json
import logging
from collections.abc import Generator
from typing import cast

from core.app.entities.task_entities import (
    AppStreamResponse,
    ChatbotAppBlockingResponse,
    ChatbotAppStreamResponse,
    ErrorStreamResponse,
    MessageEndStreamResponse,
    PingStreamResponse, DagflowAppBlockingResponse, DagflowErrorStreamResponse, DagflowPlanTreeGeneratingStreamResponse,
    DagflowPlanFinishStreamResponse, DagflowPlanStartStreamResponse,
)


class PlanGenerateResponseConverter():
    _blocking_response_type = DagflowAppBlockingResponse

    @classmethod
    def convert_blocking_full_response(cls, blocking_response: DagflowAppBlockingResponse) -> dict:  # type: ignore[override]
        """
        Convert blocking full response.
        :param blocking_response: blocking response
        :return:
        """
        response = {
            "event": "message",
            "task_id": blocking_response.task_id,
            "id": blocking_response.data.id,
            "message_id": blocking_response.data.message_id,
            "conversation_id": blocking_response.data.conversation_id,
            "mode": blocking_response.data.mode,
            "answer": blocking_response.data.answer,
            "metadata": blocking_response.data.metadata,
            "created_at": blocking_response.data.created_at,
        }

        return response

    @classmethod
    def convert_blocking_simple_response(cls, blocking_response: ChatbotAppBlockingResponse) -> dict:  # type: ignore[override]
        """
        Convert blocking simple response.
        :param blocking_response: blocking response
        :return:
        """
        response = cls.convert_blocking_full_response(blocking_response)

        metadata = response.get("metadata", {})
        response["metadata"] = cls._get_simple_metadata(metadata)

        return response

    @classmethod
    def convert_stream_full_response(
        cls, stream_response: Generator[AppStreamResponse, None, None]
    ) -> Generator[dict | str, None, None]:
        """
        Convert stream full response.
        :param stream_response: stream response
        :return:
        """
        for chunk in stream_response:
            logging.info(f">>>DAG response>>>:{chunk}")

            sub_stream_response = chunk.stream_response

            if isinstance(sub_stream_response, PingStreamResponse):
                yield "ping"
                continue

            response_chunk = {
                "conversation_id": chunk.conversation_id,
                "event": chunk.event.value,
                "dag_id": chunk.dag_id,
                "plan_id": chunk.plan_id,
                "message_id": chunk.message_id,
                "log": chunk.log,
                "scheduleConfig": chunk.schedule_config
                # "workflow_run_id": getattr(chunk, 'workflow_run_id', None),
            }

            if hasattr(chunk, 'data'):
                response_chunk["data"] = {
                    "id": chunk.data.id,
                    "created_at": chunk.data.created_at,
                }
                if hasattr(chunk.data, 'finished_at'):
                    response_chunk["data"]["finished_at"] = chunk.data.finished_at

            # if hasattr(chunk, 'dag_id'):
            #     response_chunk["dag_id"] = chunk.dag_id

            if isinstance(sub_stream_response, DagflowErrorStreamResponse):
                # data = cls._error_to_stream_response(sub_stream_response.err)
                response_chunk["message"] = sub_stream_response.err
                # response_chunk.update(data)
            elif isinstance(sub_stream_response, DagflowPlanStartStreamResponse):
                response_chunk["answer"] = "开始生成计划\n"
            elif isinstance(sub_stream_response, DagflowPlanTreeGeneratingStreamResponse):
                # response_chunk["id"] = sub_stream_response.id
                # response_chunk["from_variable_selector"] = sub_stream_response.from_variable_selector
                
                try:
                    answer_data = json.loads(sub_stream_response.answer)
                    # response_chunk["answer"] = answer_data.get("task_description", "")
                    response_chunk["data"].update(answer_data)
                except (json.JSONDecodeError, AttributeError):
                    response_chunk["answer"] = sub_stream_response.answer
            elif isinstance(sub_stream_response, DagflowPlanFinishStreamResponse):
                response_chunk.update({
                    # "id": sub_stream_response.id,
                    "metadata": sub_stream_response.metadata,
                    # "files": sub_stream_response.files
                    "answer": "生成计划完成\n"
                })

            if "data" not in response_chunk:
                response_chunk["data"] = {}
            metadata = response_chunk.get("metadata", {})
            if not isinstance(metadata, dict):
                metadata = {}
            response_chunk["data"].update(metadata)

            logging.info(f"event ====>{chunk.event.value if hasattr(chunk, 'event') and hasattr(chunk.event, 'value') else getattr(chunk, 'event', None)}")
            
            # 检查event值的方式更灵活
            # chunk_event = None
            # if hasattr(chunk, 'event'):
            #     if hasattr(chunk.event, 'value'):
            #         chunk_event = chunk.event.value
            #     elif isinstance(chunk.event, str):
            #         chunk_event = chunk.event
            
            # if chunk_event == StreamEvent.DAGFLOW_PLAN_FINISHED.value:
            #     logging.info("检测到DAG计划完成事件，更新metadata到data中")
            #     response_chunk["data"].update(response_chunk.get("metadata", {}))
            #     response_chunk.pop("metadata", None)

            yield response_chunk

    @classmethod
    def convert_stream_simple_response(
        cls, stream_response: Generator[AppStreamResponse, None, None]
    ) -> Generator[dict | str, None, None]:
        """
        Convert stream simple response.
        :param stream_response: stream response
        :return:
        """
        for chunk in stream_response:
            chunk = cast(ChatbotAppStreamResponse, chunk)
            sub_stream_response = chunk.stream_response

            if isinstance(sub_stream_response, PingStreamResponse):
                yield "ping"
                continue

            response_chunk = {
                "event": sub_stream_response.event.value,
                "conversation_id": chunk.conversation_id,
                "message_id": chunk.message_id,
                "created_at": chunk.created_at,
            }

            if isinstance(sub_stream_response, MessageEndStreamResponse):
                sub_stream_response_dict = sub_stream_response.to_dict()
                metadata = sub_stream_response_dict.get("metadata", {})
                sub_stream_response_dict["metadata"] = cls._get_simple_metadata(metadata)
                response_chunk.update(sub_stream_response_dict)
            if isinstance(sub_stream_response, ErrorStreamResponse):
                data = cls._error_to_stream_response(sub_stream_response.err)
                response_chunk.update(data)
            else:
                response_chunk.update(sub_stream_response.to_dict())

            yield response_chunk
