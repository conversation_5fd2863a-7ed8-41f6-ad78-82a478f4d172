"""DAG服务相关工具方法"""
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional

from flask import current_app

from models.dag_flow import DagPlan, DagFlows, DagFlowRuns
from services.dag.orm.dag_plan_orm_service import DagPlanORMService
from ..enums import PlanInstanceStatus, PlanStatus


def safe_json_loads(json_str: Optional[str], default_value: Any = None) -> Any:
    """安全的JSON解析

    Args:
        json_str: JSON字符串
        default_value: 默认值

    Returns:
        解析后的数据或默认值
    """
    if not json_str:
        return default_value
    try:
        return json.loads(json_str)
    except Exception as e:
        current_app.logger.error(f"JSON解析失败: {str(e)}")
        return default_value

def format_datetime(dt: Optional[datetime]) -> Optional[str]:
    """格式化日期时间

    Args:
        dt: 日期时间对象

    Returns:
        str: 格式化后的日期时间字符串
    """
    return dt.strftime('%Y-%m-%d %H:%M:%S') if dt else None

def get_latest_run_instance(dag_flow_runs: Optional[DagFlowRuns]) -> Dict[str, Any]:
    """获取最新运行实例信息

    Args:
        dag_flow_runs: DAG运行实例

    Returns:
        Dict: 运行实例信息
    """
    if not dag_flow_runs:
        return {
            "run_instance_id": None,
            "run_instance_status": None,
            "dag_id": None
        }
    
    return {
        "run_instance_id": str(dag_flow_runs.id),
        "run_instance_status": dag_flow_runs.status,
        "dag_id": dag_flow_runs.dag_id
    }

def validate_dag_dependencies(node: Dict[str, Any], all_nodes: Dict[str, Dict[str, Any]]) -> None:
    """验证DAG节点的依赖关系

    Args:
        node: 当前节点
        all_nodes: 所有节点的字典，key为task_id
    """
    all_node_ids = list(all_nodes.keys())
    node["node_event"] = []
    if not node.get("data_dependencies") and not node.get("sibling_dependencies"):
        return
    
    # 初始化节点事件列表
    if "node_event" not in node:
        node["node_event"] = []

    has_error = False  # 用于标记是否存在错误

    # 获取所有节点的task_id列表，保持原有顺序
    data_nodes = list(all_nodes.keys())

    def is_leaf_node(node_data: Dict[str, Any]) -> bool:
        """判断是否为叶子节点
        
        Args:
            node_data: 节点数据
            
        Returns:
            bool: 是否为叶子节点
        """
        children = node_data.get("children")
        return children is None or len(children) == 0

    # 验证数据依赖
    if node.get("data_dependencies"):
        for dep_id in node["data_dependencies"]:
            
            if dep_id not in all_nodes:
                # node["node_event"].append({
                #     "code": 1,
                #     "desc": f"数据依赖的节点 {dep_name} 不存在"
                # })
                # has_error = True
                continue

            dep_node = all_nodes[dep_id]
            dep_name = dep_node.get("task_name", dep_id)
            
            
            
            if not is_leaf_node(dep_node):
                node["node_event"].append({
                    "code": 1,
                    "desc": f"数据依赖的节点 {dep_name} 不是叶子节点"
                })
                has_error = True
                continue

            node_index = data_nodes.index(node["task_id"])
            dep_index = data_nodes.index(dep_id)
            # 检查依赖节点的索引是否小于当前节点
            if dep_index >= node_index:
                node["node_event"].append({
                    "code": 1,
                    "desc": f"数据依赖的节点 {dep_name} 的位置必须在当前节点之前"
                })
                has_error = True
                continue

    # 验证执行依赖
    if node.get("sibling_dependencies"):
        parent_id = node.get("parent_id")
        # 获取同父节点下的所有节点
        parent_node = all_nodes.get(parent_id)
        sibling_nodes = [child["task_id"] for child in parent_node["children"]]
        current_index = sibling_nodes.index(node["task_id"])

        for dep_id in node["sibling_dependencies"]:
            dep_node = all_nodes[dep_id]
            dep_name = dep_node.get("task_name", dep_id)
            if dep_id not in sibling_nodes:
                node["node_event"].append({
                    "code": 2,
                    "desc": f"调度依赖的节点 {dep_name} 在当前层级不存在"
                })
                has_error = True
                continue
                
            # 获取依赖节点的名称
            dep_node = all_nodes[dep_id]
            dep_name = dep_node.get("task_name", dep_id)

            dep_index = sibling_nodes.index(dep_id)
            # dep_index = all_nodes.index(dep_id)
            if dep_index >= current_index:
                node["node_event"].append({
                    "code": 2,
                    "desc": f"调度依赖的节点 {dep_name} 的位置必须在当前节点之前"
                })
                has_error = True
                continue

    return
        

def collect_all_nodes(node: Dict[str, Any], nodes_dict: Dict[str, Dict[str, Any]]) -> None:
    """收集所有节点到字典中

    Args:
        node: 当前节点
        nodes_dict: 存储所有节点的字典
    """
    nodes_dict[node["task_id"]] = node
    if "children" in node:
        for child in node["children"]:
            collect_all_nodes(child, nodes_dict)

def validate_dag_graph(dag_graph: Dict[str, Any]) -> None:
    """验证整个DAG图的依赖关系

    Args:
        dag_graph: DAG图数据
    """
    if not dag_graph:
        return

    try:
        # 收集所有节点
        all_nodes = {}
        collect_all_nodes(dag_graph, all_nodes)

        # 验证每个节点的依赖
        def validate_node(node):
            validate_dag_dependencies(node, all_nodes)
            if "children" in node:
                for child in node["children"]:
                    validate_node(child)

        validate_node(dag_graph)
    except Exception as e:
        current_app.logger.error(f"验证DAG依赖关系异常:{traceback.format_exc()}")


def format_plan_response(
    plan: DagPlan,
    dag_flow: DagFlows,
    dag_flow_runs: DagFlowRuns = None,
    updated_graphs: Optional[Dict[str, Any]] = None,
    airflow_dag_info: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """格式化计划响应数据

    Args:
        dag_flow: DAG流程
        plan_status: 计划状态
        run_instance_info: 运行实例信息
        updated_graphs: 更新后的图形数据

    Returns:
        Dict: 格式化后的响应数据
    """
    plan_status = plan.status
    logging.info(f"【plan_status】初始状态: plan_id={plan.plan_id}, dag_id={dag_flow.dag_id}, 初始plan_status={plan_status}")
    
    if plan.status in [PlanStatus.RUNNING.value, PlanStatus.FAILED.value] and dag_flow_runs:
        run_instance_status = dag_flow_runs.status
        logging.info(f"【plan_status】检查运行实例: plan_id={plan.plan_id}, dag_id={dag_flow.dag_id}, run_instance_status={run_instance_status}")
        
        if run_instance_status in [PlanInstanceStatus.PENDING.value, PlanInstanceStatus.IN_PROGRESS.value]:
            plan_status = PlanStatus.RUNNING.value  # 运行中
            logging.info(f"【plan_status】状态变更: plan_id={plan.plan_id}, 从 {plan.status} 变为 {plan_status}，因为 run_instance_status={run_instance_status}")
        elif run_instance_status == PlanInstanceStatus.COMPLETED.value:
            plan_status = PlanStatus.DONE.value  # 完成
            logging.info(f"【plan_status】状态变更: plan_id={plan.plan_id}, 从 {plan.status} 变为 {plan_status}，因为 run_instance_status={run_instance_status}")
        elif run_instance_status in [PlanInstanceStatus.FAILED.value]:
            plan_status = PlanStatus.FAILED.value  # 失败
            logging.info(f"【plan_status】状态变更: plan_id={plan.plan_id}, 从 {plan.status} 变为 {plan_status}，因为 run_instance_status={run_instance_status}")
        elif run_instance_status == PlanInstanceStatus.STOPPED.value:
            plan_status = PlanStatus.STOP.value  # 停止
            logging.info(f"【plan_status】状态变更: plan_id={plan.plan_id}, 从 {plan.status} 变为 {plan_status}，因为 run_instance_status={run_instance_status}")
    else:
        logging.info(f"【plan_status】状态未变: plan_id={plan.plan_id}, plan_status={plan_status}, 原因: plan.status={(plan.status == PlanStatus.RUNNING.value)}, dag_flow_runs存在={bool(dag_flow_runs)}")

    # 当plan_status 和plan.status 不一致时，需要更新plan.status
    if plan_status != plan.status:
        logging.info(f"【plan_status】需要更新状态: plan_id={plan.plan_id}, 从 {plan.status} 更新为 {plan_status}")
        try:
            dag_plan_service = DagPlanORMService()
            dag_plan_service.update_status(plan.plan_id, plan_status)
            logging.info(f"【plan_status】状态更新成功: plan_id={plan.plan_id}, 新状态={plan_status}")
        except Exception as e:
            logging.error(f"【plan_status】状态更新失败: plan_id={plan.plan_id}, 错误={str(e)}")

    schedule_config = json.loads(dag_flow.schedule_config) if dag_flow.schedule_config else None
    if schedule_config:
        schedule_config['cron_desc'] = airflow_dag_info.get('timetable_description') if airflow_dag_info and airflow_dag_info.get('timetable_description') else schedule_config.get('cron_desc', '')

    tool_info = {
        "tool_id": plan.tool_id,
        "plan_name":plan.name,
        "plan_en_name": plan.tool_name,
        "plan_description": plan.description,
    }
    inputs = json.loads(dag_flow.inputs) if dag_flow.inputs else None

    if updated_graphs and updated_graphs.get("dag_graph"):
        validate_dag_graph(updated_graphs["dag_graph"])
    
    response = {
        "plan_id": dag_flow.plan_id,
        "dag_id": dag_flow.dag_id,
        "run_instance_id": dag_flow_runs.id if dag_flow_runs else None,
        "name": plan.name,
        "description": dag_flow.description,
        "logs": dag_flow.logs,
        "dag_status": dag_flow.status,
        "plan_status": plan_status,
        "run_instance_status": dag_flow_runs.status if dag_flow_runs else None,
        "plan_modify_status": plan.operate_status,
        "dag_graph": updated_graphs.get("dag_graph"),
        "airflow_graph": updated_graphs.get("airflow_graph"),
        "schedule_config": schedule_config,
        "tool_info": tool_info,
        "inputs": inputs,
        "task_id": dag_flow.task_id
    }
    # logging.info(f"【detail】response:{response}  plan:{plan.to_dict()} dag_flow:{dag_flow.to_dict()} dag_flow_runs:{dag_flow_runs.to_dict() if dag_flow_runs else None}, airflow_dag_info:{airflow_dag_info}")

    return response

def determine_plan_status(run_instance_status: Optional[str]) -> Optional[str]:
    """根据运行实例状态确定计划状态

    Args:
        run_instance_status: 运行实例状态

    Returns:
        str: 计划状态
    """
    if not run_instance_status:
        return None
        
    status_mapping = {
        "Pending": "running",
        "InProgress": "running",
        "Completed": "done",
        "Failed": "failed",
        "Stopped": "stop"
    }
    return status_mapping.get(run_instance_status) 