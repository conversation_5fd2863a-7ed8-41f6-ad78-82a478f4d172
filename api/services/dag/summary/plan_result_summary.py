# -*- coding: utf-8 -*-
"""
计划结果汇总服务模块
提供计划执行完成后的结果汇总功能
"""

import json
import logging
import time
from typing import Dict, Any, List, Generator, Tuple, Optional
import uuid
import re
from datetime import datetime, timezone

import requests
import os
from urllib.parse import urlparse

from collections import defaultdict
from core.file import helpers as file_helpers

from langchain_openai import AzureChatOpenAI

from configs.remote_settings_sources.apollo import apollo_client
from extensions.ext_database import db
from extensions.ext_redis import redis_client
from models import EndUser, Message, Conversation, UploadFile
from models.dag_flow import DagFlows, DagFlowRuns, DagFlowNodeExecutions
from models.toc_user import ToCUser
from models.workbench import Workbench, WorkbenchFile
from openai import OpenAI

from services.dag.enums import TriggerType

import yw.llm.YwLiteLlmClient as YwLiteLlmClient

logger = logging.getLogger(__name__)

# 用于缓存 YwLiteLlmClient 实例的字典
_llm_clients_cache: Dict[str, YwLiteLlmClient] = {}

def _get_llm_client(user_id: str, conversation_id: str) -> YwLiteLlmClient:
    """
    根据 conversation_id 获取或创建 YwLiteLlmClient 实例。
    如果缓存中存在，则直接复用；否则，通过 get_cache_llm 获取并缓存。
    """
    if conversation_id not in _llm_clients_cache:
        # 如果缓存中没有，则通过 get_cache_llm 获取并存入缓存
        llm_instance = YwLiteLlmClient.get_cache_llm(
            "summary",
            "summary",
            user=user_id,
            session_id=conversation_id
        )
        _llm_clients_cache[conversation_id] = llm_instance
    return _llm_clients_cache[conversation_id]


def merge_result_node(result_nodes_1: List[DagFlowNodeExecutions], result_nodes_2: List[DagFlowNodeExecutions]) -> List[DagFlowNodeExecutions]:
    """
    智能合并两种方法识别的结果节点
    
    合并策略：
    1. 取并集：确保不遗漏任何可能的结果节点
    2. 去重：基于节点ID去重
    3. 优先级：大模型识别的节点优先级更高（因为语义理解更准确）
    4. 验证：对图结构识别的节点进行二次验证
    
    Args:
        result_nodes_1: 图结构分析识别的结果节点列表
        result_nodes_2: 大模型分析识别的结果节点列表
        
    Returns:
        List[DagFlowNodeExecutions]: 合并后的结果节点列表
    """
    try:
        # 1. 转换为字典便于处理
        graph_nodes_dict = {node.node_id: node for node in result_nodes_1}
        llm_nodes_dict = {node.node_id: node for node in result_nodes_2}
        
        # 2. 记录合并前的统计信息
        logger.info(f"图结构分析识别到 {len(result_nodes_1)} 个结果节点: {list(graph_nodes_dict.keys())}")
        logger.info(f"大模型分析识别到 {len(result_nodes_2)} 个结果节点: {list(llm_nodes_dict.keys())}")
        
        # 3. 合并节点（优先保留大模型识别的节点）
        merged_nodes = {}
        
        # 首先添加大模型识别的节点（优先级更高）
        for node_id, node in llm_nodes_dict.items():
            merged_nodes[node_id] = node
        
        # 然后添加图结构识别的节点（如果不在大模型结果中）
        for node_id, node in graph_nodes_dict.items():
            if node_id not in merged_nodes:
                merged_nodes[node_id] = node
        
        # 4. 验证和过滤
        # validated_nodes = []
        # for node_id, node_info in merged_nodes.items():
        #     node = node_info['node']
        #     source = node_info['source']
        #     priority = node_info['priority']
        #
        #     # 验证节点是否有效
        #     if PlanResultSummaryService._validate_result_node(node, source):
        #         validated_nodes.append(node)
        #         logger.info(f"保留结果节点: {node_id} (来源: {source}, 优先级: {priority})")
        #     else:
        #         logger.warning(f"过滤掉无效结果节点: {node_id} (来源: {source})")
        #
        # # 5. 按优先级和创建时间排序
        # validated_nodes.sort(key=lambda x: (
        #     # 首先按优先级排序（大模型识别的优先）
        #     1 if x.node_id in llm_nodes_dict else 2,
        #     # 然后按创建时间排序
        #     x.created_at or datetime.min.replace(tzinfo=timezone.utc)
        # ))
        
        logger.info(f"最终合并得到 {len(merged_nodes)} 个有效结果节点")
        return merged_nodes.values()
        
    except Exception as e:
        logger.error(f"合并结果节点失败: {str(e)}")
        # 发生错误时，返回大模型的结果作为备选
        logger.warning("使用大模型分析结果作为备选方案")
        return result_nodes_2 if result_nodes_2 else result_nodes_1


class PlanResultSummaryService:
    """计划结果汇总服务"""

    LOCK_EXPIRE_TIME = 300  # 锁的过期时间（秒）

    @staticmethod
    def acquire_lock(lock_key: str, expire_time: int = LOCK_EXPIRE_TIME) -> bool:
        """获取Redis分布式锁

        Args:
            lock_key: 锁的键名
            expire_time: 锁的过期时间（秒）

        Returns:
            bool: 是否成功获取锁
        """
        try:
            # 使用Redis的setnx命令尝试获取锁
            lock_value = str(time.time())
            acquired = redis_client.set(
                lock_key,
                lock_value,
                ex=expire_time,  # 设置过期时间
                nx=True  # 只在键不存在时设置
            )
            return bool(acquired)
        except Exception as e:
            logger.error(f"获取Redis锁失败: {str(e)}")
            return False

    @staticmethod
    def release_lock(lock_key: str) -> bool:
        """释放Redis分布式锁

        Args:
            lock_key: 锁的键名

        Returns:
            bool: 是否成功释放锁
        """
        try:
            redis_client.delete(lock_key)
            return True
        except Exception as e:
            logger.error(f"释放Redis锁失败: {str(e)}")
            return False

    @staticmethod
    def _check_summary_exists(dag_run_id: str) -> bool:
        """检查是否已经为该运行ID生成过汇总

        Args:
            dag_run_id: DAG运行ID

        Returns:
            bool: 是否存在汇总
        """
        try:
            # 查询是否存在相关的消息记录
            message_exists = db.session.query(Message).filter(
                Message.answer.like(f"%{dag_run_id}%"),  # 简单的模糊匹配，可以根据实际需求调整
                Message.from_source == "system"
            ).first() is not None

            return message_exists
        except Exception as e:
            logger.error(f"检查汇总是否存在时出错: {str(e)}")
            return False

    @staticmethod
    def _parse_json_response(json_text: str) -> Dict[str, Any]:
        """解析大模型返回的JSON响应，处理可能带有markdown代码块标记的情况

        Args:
            json_text: 大模型返回的JSON文本

        Returns:
            Dict[str, Any]: 解析后的JSON对象

        Raises:
            ValueError: 当JSON解析失败时抛出
        """
        try:
            # 尝试直接解析
            return json.loads(json_text)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试处理markdown代码块
            try:
                # 移除可能的markdown代码块标记
                cleaned_json = re.sub(r'^```json\s*', '', json_text)
                cleaned_json = re.sub(r'\s*```$', '', cleaned_json)
                return json.loads(cleaned_json)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                logger.error(f"原始JSON文本: {json_text}")
                raise ValueError("无法解析JSON响应")

    @staticmethod
    def _identify_result_nodes(plan_id: str, dag_id: str, dag_run_id: str, end_user: EndUser) -> List[DagFlowNodeExecutions]:
        """识别包含执行结果的节点
        
        Args:
            plan_id: 计划ID
            dag_id: DAG ID
            dag_run_id: DAG运行ID
            
        Returns:
            List[Dict[str, Any]]: 包含结果的节点列表
        """
        try:
            # 1. 收集基础节点信息
            dag_flow = db.session.query(DagFlows).filter(
                DagFlows.plan_id == plan_id,
                DagFlows.dag_id == dag_id,
                DagFlows.is_deleted == False
            ).first()

            if not dag_flow:
                raise ValueError(f"未找到计划: {plan_id}")

            # 获取DAG图信息
            dag_graph = json.loads(dag_flow.dag_graph) if dag_flow.dag_graph else None

            # 获取节点执行信息（不包含input/output）
            node_executions = db.session.query(DagFlowNodeExecutions).filter(
                DagFlowNodeExecutions.dag_run_id == dag_run_id,
                DagFlowNodeExecutions.is_deleted == False
            ).order_by(DagFlowNodeExecutions.created_at.asc()).all()  # 按创建时间排序

            # 2. 构建节点信息
            basic_node_info = {
                "plan_info": {
                    "plan_id": plan_id,
                    "dag_id": dag_id,
                    "name": dag_flow.name,
                    "description": dag_flow.description,
                    "dag_graph": dag_graph  # 包含DAG图信息，帮助理解节点关系
                },
                "node_executions": [
                    {
                        "node_id": execution.node_id,
                        "node_name": execution.node_name,
                        "node_description": execution.node_description,
                    }
                    for idx, execution in enumerate(node_executions)
                ]
            }

            # 3. 调用大模型识别结果节点
            prompt = f"""
            请作为专业的计划执行分析师，分析以下计划执行节点信息，识别出包含最终执行结果的节点。

            分析步骤：
            1. 理解计划目标：
               - 仔细阅读计划名称和描述
               - 分析DAG图结构，理解节点间的依赖关系
               - 确定整个计划的核心目标

            2. 分析执行流程：
               - 根据节点执行顺序，理解整个执行链路
               - 分析每个节点的名称、描述和类型
               - 理解节点在流程中的角色（数据收集、处理、最终产出等）

            3. 识别结果节点：
               - 不要仅依赖计划名称来判断
               - 通过整体链路分析，确定真正包含最终结果的节点
               - 关注节点是否产出了对计划目标至关重要的内容
               - 注意：最终结果节点不一定是最后执行的节点

            4. 结果节点特征：
               - 包含完整的执行结果数据
               - 输出内容与计划目标直接相关
               - 可能是多个节点，如果结果分散在不同节点中

            计划信息：
            {json.dumps(basic_node_info, ensure_ascii=False, indent=2)}

            请返回一个JSON格式的结果，包含：
            1. 计划目标分析
            2. 执行流程分析
            3. 结果节点列表（包含节点ID和选择理由）

            格式如下：
            {{
                "plan_analysis": {{
                    "target": "计划的核心目标分析",
                    "execution_flow": "执行流程分析"
                }},
                "result_nodes": [
                    {{
                        "node_id": "节点ID",
                        "reason": "选择该节点作为结果节点的原因"
                    }}
                ]
            }}
            """

            conversation_id = dag_flow.conversation_id

            # 4. 调用大模型获取结果节点列表
            result = PlanResultSummaryService._call_llm_identify_nodes(prompt, end_user=end_user, conversation_id=conversation_id)
            
            # 5. 验证和提取结果节点
            if not isinstance(result, dict):
                raise ValueError("大模型返回结果格式错误")
                
            if "result_nodes" not in result:
                raise ValueError("大模型返回结果缺少result_nodes字段")
                
            # 6. 获取完整的结果节点信息
            result_node_ids = [node["node_id"] for node in result["result_nodes"]]
            result_nodes = [
                execution for execution in node_executions 
                if execution.node_id in result_node_ids
            ]
            
            # 7. 记录分析结果
            logger.info(f"计划目标分析: {result.get('plan_analysis', {}).get('target')}")
            logger.info(f"执行流程分析: {result.get('plan_analysis', {}).get('execution_flow')}")
            logger.info(f"识别到的结果节点: {result_node_ids}")
            
            return result_nodes

        except Exception as e:
            logger.error(f"识别结果节点失败: {str(e)}")
            raise e

    @staticmethod
    def _call_llm_identify_nodes(prompt: str, end_user: EndUser, conversation_id: str) -> Dict[str, Any]:
        """调用大模型识别结果节点
        
        Args:
            prompt: 提示词
            
        Returns:
            Dict[str, Any]: 大模型返回的结果
        """
        try:


            # 构造消息
            messages = [
                {"role": "system", "content": '你是一个专业的计划执行分析师，擅长分析计划执行流程和识别关键结果节点。'},
                {"role": "user", "content": prompt}
            ]

            # # 创建OpenAI客户端
            # client = OpenAI(
            #     api_key="<your api key>",
            #     base_url="http://124.223.22.249:6399/v1"
            # )
            # # 发送请求
            # response = client.chat.completions.create(
            #     model="deepseek-v3",
            #     messages=messages,
            #     temperature=0.0  # 使用确定性输出
            # )
            #
            # # 解析响应
            # result_text = response.choices[0].message.content


            llm = _get_llm_client(user_id=str(end_user.id), conversation_id=str(conversation_id))
            # 启用流式模式并处理分块响应
            result = llm.invoke(
                messages,
                stream=False  # 关键参数：启用流式输出
            )
            # 尝试解析JSON
            result_text = result.content

            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取JSON部分
                json_match = re.search(r'```json\s*(\{[\s\S]*?\})\s*```', result_text)
                if json_match:
                    return json.loads(json_match.group(1))
                raise ValueError("无法解析大模型返回的JSON结果")

        except Exception as e:
            logger.error(f"调用大模型识别结果节点失败: {str(e)}")
            raise e

    @staticmethod
    def _extract_result_node_logs(dag_id: str, dag_run_id: str, result_nodes: List[DagFlowNodeExecutions]) -> Dict[str, List[Dict[str, Any]]]:
        """提取结果节点的日志信息
        
        Args:
            dag_id: DAG ID
            dag_run_id: DAG运行ID
            result_nodes: 结果节点列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 节点日志信息
        """
        try:
            # 获取所有节点日志
            all_logs = PlanResultSummaryService._fetch_node_logs(dag_id, dag_run_id)
            
            # 只返回结果节点的日志
            result_node_ids = [node.node_id for node in result_nodes]
            filtered_logs = {}
            for node_id, logs in all_logs.items():
                if node_id in result_node_ids:
                    filtered_logs[node_id] = logs
            
            logger.info(f"从 {len(all_logs)} 个节点中提取了 {len(filtered_logs)} 个结果节点的日志")
            return filtered_logs
            
        except Exception as e:
            logger.error(f"提取结果节点日志失败: {str(e)}")
            raise e

    @staticmethod
    def _process_result_files(result_nodes: List[DagFlowNodeExecutions], end_user: EndUser) -> List[Dict[str, Any]]:
        """处理结果节点中的文件
        
        Args:
            result_nodes: 结果节点列表
            end_user: 用户信息
            
        Returns:
            List[Dict[str, Any]]: 处理后的文件信息
        """
        try:
            file_mappings = []
            
            for node in result_nodes:
                if not node.outputs:
                    continue
                    
                try:
                    outputs = json.loads(node.outputs)
                except json.JSONDecodeError:
                    logger.warning(f"节点 {node.node_id} 的输出不是有效的JSON格式")
                    continue
                
                # 处理节点输出中的文件
                files = PlanResultSummaryService._extract_files_from_outputs(outputs)
                for file_info in files:
                    try:
                        # 处理单个文件
                        processed_file = PlanResultSummaryService._process_single_file(file_info, end_user)
                        if processed_file:
                            file_mappings.append(processed_file)
                    except Exception as e:
                        logger.error(f"处理文件失败: {str(e)}")
                        continue
            
            return file_mappings
            
        except Exception as e:
            logger.error(f"处理结果文件失败: {str(e)}")
            raise e

    @staticmethod
    def _extract_files_from_outputs(outputs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从节点输出中提取文件信息
        
        Args:
            outputs: 节点输出
            
        Returns:
            List[Dict[str, Any]]: 文件信息列表
        """
        files = []
        
        def extract_files_from_dict(data: Dict[str, Any]) -> None:
            """递归提取文件信息"""
            for key, value in data.items():
                if isinstance(value, dict):
                    extract_files_from_dict(value)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            extract_files_from_dict(item)
                elif isinstance(value, str):
                    # 检查是否是文件URL
                    if value.startswith(('http://', 'https://')):
                        # 尝试从URL中提取文件名
                        filename = value.split('/')[-1]
                        files.append({
                            "name": filename,
                            "url": value,
                            "type": "document"  # 默认类型
                        })

        extract_files_from_dict(outputs)
        return files

    @staticmethod
    def _process_single_file(file_info: Dict[str, Any], end_user: EndUser) -> Optional[Dict[str, Any]]:
        """处理单个文件
        
        Args:
            file_info: 文件信息
            end_user: 用户信息
            
        Returns:
            Optional[Dict[str, Any]]: 处理后的文件信息
        """
        try:
            url = file_info.get("url")
            if not url:
                return None
                
            # 下载文件并保存到OSS
            upload_file = PlanResultSummaryService._download_and_save_file(
                url=url,
                filename=file_info["name"],
                end_user=end_user
            )
            
            if upload_file:
                return {
                    "name": file_info["name"],
                    "type": file_info.get("type", "document"),
                    "url": file_helpers.get_signed_file_url_with_type(str(upload_file.id), upload_file.mime_type, upload_file.extension),
                    "mime_type": upload_file.mime_type,
                    "size": upload_file.size,
                    "description": file_info.get("description", "")
                }
            
            return None
            
        except Exception as e:
            logger.error(f"处理单个文件失败: {str(e)}")
            return None

    @staticmethod
    def generate_plan_summary_stream(
            plan_id: str,
            dag_id: str,
            dag_run_id: str,
            end_user: EndUser = None,
            use_graph_analysis: bool = True  # 新增参数控制使用哪种方式识别结果节点
    ) -> None:
        """生成计划执行汇总并写入Redis

        Args:
            plan_id: 计划ID
            dag_id: DAG ID
            dag_run_id: DAG运行ID
            end_user: 用户信息
            use_graph_analysis: 是否使用图结构分析来识别结果节点
        """
        # 生成锁的键名
        lock_key = f"plan_summary_lock:{dag_run_id}"
        redis_key = f"plan_summary:{dag_run_id}"

        # 生成消息ID和任务ID
        message_id = str(uuid.uuid4())
        task_id = str(uuid.uuid4())
        created_at = int(time.time())
        conversation_id = None

        try:
            # 尝试获取锁
            if not PlanResultSummaryService.acquire_lock(lock_key):
                logger.warning(f"无法获取锁，可能有其他进程正在处理该汇总: {dag_run_id}")
                redis_client.setex(
                    f"{redis_key}:error",
                    300,
                    json.dumps({
                        "error": "Another process is generating the summary",
                        "status": "error",
                        "id": message_id,
                        "task_id": task_id,
                        "created_at": created_at
                    })
                )
                return

            # 检查是否已经生成过汇总
            if PlanResultSummaryService._check_summary_exists(dag_run_id):
                logger.info(f"该运行ID的汇总已存在，跳过生成: {dag_run_id}")
                redis_client.setex(
                    f"{redis_key}:completed",
                    300,
                    json.dumps({
                        "message": "Summary already exists",
                        "status": "completed",
                        "id": message_id,
                        "task_id": task_id,
                        "created_at": created_at
                    })
                )
                return


            dag_flow_runs = db.session.query(DagFlowRuns).filter(DagFlowRuns.id == dag_run_id).first()

            # 1. 识别结果节点
            result_nodes_1 = PlanResultSummaryService._identify_result_nodes_by_graph(plan_id, dag_id, dag_run_id)
            result_nodes_2 = PlanResultSummaryService._identify_result_nodes(plan_id, dag_id, dag_run_id, end_user)
            
            result_nodes = merge_result_node(result_nodes_1, result_nodes_2)
            
            if not result_nodes:
                raise ValueError("未找到包含结果的节点")

            # 2. 提取结果节点日志
            node_logs = PlanResultSummaryService._extract_result_node_logs(dag_id, dag_run_id, result_nodes)

            # 3. 生成汇总数据
            summary_data = PlanResultSummaryService._generate_summary_data(
                plan_id=plan_id,
                dag_id=dag_id,
                dag_run_id=dag_run_id,
                result_nodes=result_nodes,
                node_logs=node_logs
            )

            # 获取会话ID
            conversation_id = summary_data.get("plan_info", {}).get("conversation_id")

            # 4. 先处理文件上传
            file_prompt = PlanResultSummaryService._generate_summary_prompt(summary_data, is_file_summary=True)
            file_json = ""
            for chunk in PlanResultSummaryService._call_llm_generate_summary_stream(prompt=file_prompt,  end_user=end_user, conversation_id=conversation_id):
                file_json += chunk

            # 解析文件JSON
            try:
                logger.info(f"file json:\n{file_json} ")
                file_data = PlanResultSummaryService._parse_json_response(file_json)
                files = file_data.get("files", [])
            except Exception as e:
                logger.error(f"解析文件JSON失败: {str(e)}")
                files = []

            # 5. 处理文件上传并获取URL映射
            file_mappings = []
            if files:

                # 处理文件上传
                for file_info in files:
                    if not file_info.get("url"):
                        continue

                    file_type = file_info.get("type", "").lower()
                    original_url = file_info["url"]
                    is_webpage = file_type == "webpage"

                    # 验证URL格式
                    try:
                        parsed_url = urlparse(original_url)
                        if not all([parsed_url.scheme, parsed_url.netloc]):
                            logger.warning(f"无效的URL格式: {original_url}")
                            continue
                    except Exception as e:
                        logger.error(f"URL解析失败: {str(e)}")
                        continue

                    # 根据文件类型和MIME类型判断是否需要下载并保存文件
                    mime_type = file_info.get("mime_type", "").lower()
                    file_extension = PlanResultSummaryService._get_file_extension_from_mime_type(mime_type)

                    # 对于网页链接或无法获取文件扩展名的URL，直接使用原始URL
                    if is_webpage or not file_extension:
                        logger.info(f"使用原始URL: {original_url} (类型: {file_type})")
                        final_url = original_url
                    else:
                        try:
                            # 下载文件并保存到OSS
                            upload_file = PlanResultSummaryService._download_and_save_file(
                                url=original_url,
                                filename=file_info["name"],
                                end_user=end_user
                            )
                            if upload_file:
                                final_url = file_helpers.get_signed_file_url_with_type(str(upload_file.id), upload_file.mime_type, upload_file.extension)
                            else:
                                logger.warning(f"文件上传失败，使用原始URL: {original_url}")
                                final_url = original_url
                        except Exception as e:
                            logger.error(f"下载并保存文件失败: {str(e)}")
                            final_url = original_url

                    # 添加到文件映射列表
                    file_mappings.append({
                        "original_url": original_url,
                        "new_url": final_url,
                        "name": file_info["name"],
                        "type": file_type
                    })

            # 6. 将文件映射信息添加到summary_data中
            summary_data["file_mappings"] = file_mappings

            # 7. 生成markdown汇总
            markdown_prompt = PlanResultSummaryService._generate_summary_prompt(summary_data, is_file_summary=False)
            markdown_summary = ""

            # 初始设置一个空内容，让前端可以立即收到响应
            redis_client.setex(
                redis_key,
                300,
                json.dumps({
                    "answer": "",
                    "status": "streaming",
                    "id": message_id,
                    "task_id": task_id,
                    "created_at": created_at,
                    "conversation_id": conversation_id
                })
            )

            # 流式生成markdown内容
            for chunk in PlanResultSummaryService._call_llm_generate_summary_stream(markdown_prompt,  end_user=end_user, conversation_id=conversation_id):
                markdown_summary += chunk
                redis_client.setex(
                    redis_key,
                    300,
                    json.dumps({
                        "answer": markdown_summary,
                        "status": "streaming",
                        "id": message_id,
                        "task_id": task_id,
                        "created_at": created_at,
                        "conversation_id": conversation_id
                    })
                )

            # 8. 保存汇总到会话
            if end_user and dag_flow_runs.trigger_type in (TriggerType.MANUAL.value, TriggerType.SCHEDULED.value):

                PlanResultSummaryService._save_summary_to_conversation(
                    conversation_id=conversation_id,
                    plan_id=plan_id,
                    summary=markdown_summary,
                    files=files,
                    end_user=end_user,
                    message_id=message_id,
                    dag_run_id=dag_run_id
                )
            else:
                logger.info(f"计划执行结果：plan_id: {plan_id}, result_summary: {markdown_summary}")

            # 设置完成标志
            redis_client.setex(
                f"{redis_key}:completed",
                300,
                json.dumps({
                    "answer": markdown_summary,
                    "status": "completed",
                    "id": message_id,
                    "task_id": task_id,
                    "created_at": created_at,
                    "conversation_id": conversation_id
                })
            )

        except Exception as e:
            logger.error(f"生成计划汇总失败: {str(e)}")
            redis_client.setex(
                f"{redis_key}:error",
                300,
                json.dumps({
                    "error": str(e),
                    "status": "error",
                    "id": message_id,
                    "task_id": task_id,
                    "created_at": created_at,
                    "conversation_id": conversation_id
                })
            )
        finally:
            # 释放锁
            try:
                PlanResultSummaryService.release_lock(lock_key)
            except Exception as e:
                logger.error(f"释放Redis锁失败: {str(e)}")

    @staticmethod
    def _generate_summary_data(
        plan_id: str,
        dag_id: str,
        dag_run_id: str,
        result_nodes: List[DagFlowNodeExecutions],
        node_logs: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """生成汇总数据
        
        Args:
            plan_id: 计划ID
            dag_id: DAG ID
            dag_run_id: DAG运行ID
            result_nodes: 结果节点列表
            node_logs: 节点日志信息
            
        Returns:
            Dict[str, Any]: 汇总数据
        """
        try:
            # 获取计划信息
            dag_flow = db.session.query(DagFlows).filter(
                DagFlows.plan_id == plan_id,
                DagFlows.dag_id == dag_id,
                DagFlows.is_deleted == False
            ).first()

            if not dag_flow:
                raise ValueError(f"未找到计划: {plan_id}")

            # 获取DAG运行信息
            dag_run = db.session.query(DagFlowRuns).filter(
                DagFlowRuns.id == dag_run_id,
                DagFlowRuns.is_deleted == False
            ).first()

            if not dag_run:
                raise ValueError(f"未找到运行记录: {dag_run_id}")

            # 构建汇总数据
            summary_data = {
                "plan_info": {
                    "plan_id": plan_id,
                    "dag_id": dag_id,
                    "name": dag_flow.name,
                    "description": dag_flow.description,
                    "conversation_id": str(dag_flow.conversation_id) if dag_flow.conversation_id else None,
                    "dag_graph": json.loads(dag_flow.dag_graph) if dag_flow.dag_graph else None
                },
                "run_info": {
                    "dag_run_id": dag_run_id,
                    "status": dag_run.status,
                    "start_time": dag_run.created_at.isoformat() if dag_run.created_at else None,
                    "end_time": dag_run.updated_at.isoformat() if dag_run.updated_at else None,
                    "cost": dag_run.cost
                },
                "node_executions": [
                    {
                        "node_id": node.node_id,
                        "node_name": node.node_name,
                        "node_description": node.node_description,
                        "status": node.status,
                        "execution_type": node.execution_type,
                        "start_time": node.start_time.isoformat() if node.start_time else None,
                        "completion_time": node.completion_time.isoformat() if node.completion_time else None,
                        "inputs": json.loads(node.inputs) if node.inputs else None,
                        "outputs": json.loads(node.outputs) if node.outputs else None,
                        "logs": node_logs.get(node.node_id, [])
                    }
                    for node in result_nodes
                ]
            }

            return summary_data

        except Exception as e:
            logger.error(f"生成汇总数据失败: {str(e)}")
            raise e

    @staticmethod
    def _fetch_node_logs(dag_id: str, dag_run_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """获取并按节点ID分组的日志信息。

        此函数会调用外部API获取原始日志列表（预期格式为 List[Dict]），
        然后将其处理成按节点ID（预期为日志条目中的 'task_id' 键）分组的字典。

        Args:
            dag_id: DAG ID
            dag_run_id: DAG运行ID

        Returns:
            Dict[str, List[Dict[str, Any]]]: 一个字典，其中键是节点ID (str)，
                                             值是包含该节点所有日志条目(Dict)的列表。
                                             如果获取或处理失败，则返回一个空字典。
        """
        logs_by_node = defaultdict(list)  # 使用defaultdict简化分组逻辑
        try:
            # 1. 构建API请求
            airflow_out_api_url = apollo_client.get_value("AIRFLOW_LOG_API")  # 如果使用apollo获取配置

            # 确认API端点是否正确，根据你的描述，可能是 /task/log/all
            url = f"{airflow_out_api_url}/task/log/all"

            params = {
                "dag_id": dag_id,
                "run_id": dag_run_id
            }

            # 2. 发送请求
            logger.info(f"开始获取节点日志: dag_id={dag_id}, run_id={dag_run_id}, url={url}")
            response = requests.get(url, params=params, timeout=30)  # 设置超时
            response.raise_for_status()  # 检查HTTP错误 (例如 4xx, 5xx)，如果失败会抛出异常

            # 3. 解析响应
            result = response.json()
            logger.debug(f"收到节点日志API响应: {result}")

            # 4. 检查API业务层是否成功
            if result.get("success"):
                raw_log_data = result.get("data")

                # 5. 检查并处理返回的日志数据
                if isinstance(raw_log_data, list):
                    # 遍历原始日志列表并按 'task_id' 分组
                    for log_entry in raw_log_data:
                        if isinstance(log_entry, dict):
                            node_id_from_log = log_entry.get('task_id')  # 假设节点ID在日志条目的 'task_id' 键中
                            if node_id_from_log:
                                logs_by_node[node_id_from_log].append(log_entry)  # 将完整的日志条目字典加入列表
                            else:
                                logger.warning(f"处理日志时发现缺少 'task_id' 的条目: {log_entry}")
                        else:
                            logger.warning(f"处理日志时发现格式不正确的条目（非字典）: {log_entry}")

                    logger.info(f"成功获取并处理了 {len(raw_log_data)} 条日志记录，分成了 {len(logs_by_node)} 个节点组")
                    return dict(logs_by_node)  # 将defaultdict转换为普通dict返回
                elif raw_log_data is None:
                    # API成功返回，但data字段为null或不存在，视为没有日志
                    logger.warning(
                        f"获取节点日志API成功，但未返回任何数据 (data is null/missing) for dag_id={dag_id}, run_id={dag_run_id}")
                    return {}
                else:
                    # API成功返回，但data字段不是预期的列表格式
                    logger.warning(
                        f"获取节点日志API成功，但返回的数据格式不正确（期望列表，实际为 {type(raw_log_data)}） for dag_id={dag_id}, run_id={dag_run_id}")
                    return {}  # 返回空字典表示数据格式错误
            else:
                # API 返回 success: false
                error_message = result.get('message', '无具体错误信息')
                logger.warning(f"获取节点日志的API报告失败: {error_message} (dag_id={dag_id}, run_id={dag_run_id})")
                return {}

        except requests.exceptions.RequestException as e:
            # 处理网络请求相关的异常 (连接错误, 超时, HTTP错误等)
            logger.error(f"请求节点日志API失败: {e}")
            return {}
        except json.JSONDecodeError as e:
            # 处理JSON解析错误
            logger.error(f"解析节点日志API响应失败: {e}")
            try:
                # 尝试记录原始响应文本帮助调试
                logger.error(f"原始响应文本: {response.text}")
            except NameError:  # 如果请求阶段就失败了，response可能未定义
                pass
            return {}
        except Exception as e:
            # 捕获其他所有未预料到的异常
            logger.exception(f"获取节点日志过程中发生未知错误: {e}")  # logger.exception 会记录堆栈跟踪信息
            return {}

    @staticmethod
    def _generate_summary_prompt(summary_data: Dict[str, Any], is_file_summary: bool = False) -> str:
        """生成汇总提示词

        Args:
            summary_data: 收集的汇总数据
            is_file_summary: 是否生成文件汇总

        Returns:
            str: 生成的提示词
        """
        # 构建基础提示词
        base_prompt = f"""
您是专业的项目执行结果分析师。请分析以下计划执行数据，并生成严格遵循格式和要求的中文摘要报告。

计划信息：
- 计划名称: {summary_data['plan_info']['name']}
- 计划描述: {summary_data['plan_info']['description']}
- 计划ID: {summary_data['plan_info']['plan_id']}

执行概述：
- Dag ID: {summary_data['plan_info']['dag_id']}
- 运行ID: {summary_data['run_info']['dag_run_id']}
- 开始时间: {summary_data['run_info']['start_time']}
- 结束时间: {summary_data['run_info']['end_time']}
- 状态: {summary_data['run_info']['status']}
- 总耗时: {summary_data['run_info']['cost']}

节点执行详情:
"""

        # 添加节点执行信息
        for node in summary_data['node_executions']:
            base_prompt += f"""
节点: {node['node_name']}
- 节点ID: {node['node_id']}
- 描述: {node['node_description']}
- 状态: {node['status']}
- 执行类型: {node['execution_type']}
- 开始时间: {node['start_time']}
- 结束时间: {node['completion_time']}
- 输入: {json.dumps(node['inputs'], ensure_ascii=False) if node['inputs'] else 'None'}
- 输出: {json.dumps(node['outputs'], ensure_ascii=False) if node['outputs'] else 'None'}
- 日志:
{chr(10).join([f"  {log}" for log in node['logs']]) if node['logs'] else '  无可用日志'}
"""

        # 添加文件映射信息
        if not is_file_summary and "file_mappings" in summary_data:
            base_prompt += "\n文件URL映射信息：\n"
            for mapping in summary_data["file_mappings"]:
                base_prompt += f"- 原URL: {mapping['original_url']} -> 新URL: {mapping['new_url']} (文件名: {mapping['name']}, 类型: {mapping['type']})\n"

        if is_file_summary:
            # 文件汇总提示词
            prompt = base_prompt + """
重要指导：

1. **结果文件提取标准**：
   a) **文件来源验证**：
      - 仅从提供的**结果节点（可能有一个或多个）**的输出（outputs）中提取文件。
      - 必须验证文件在节点输出中实际存在，不能凭空生成或推测文件信息。
      - 如果结果节点的输出为空或状态为失败（例如工具调用超时），不得生成任何文件信息。
      - 忽略中间处理节点的临时文件。
      - 排除日志、调试信息等非结果文件。

   b) **文件类型限制**：
      - **文档类**：PDF、Word、Excel、PowerPoint
      - **图片类**：JPG、PNG、GIF
      - **视频类**：MP4、AVI、MOV
      - **网页类**：URL链接
      - **其他**：仅包含明确的结果文件。

   c) **文件质量要求**：
      - 必须是完整的、可用的文件。
      - 必须包含有效的访问URL。
      - 必须能明确识别文件类型和用途。

2. **文件信息提取要求**：
   a) **必要字段**（必须包含）：
      - `name`: 文件名（必须清晰描述文件内容）
      - `type`: 文件类型（必须是以下之一：`document`, `image`, `video`, `webpage`）
      - `url`: 文件URL（必须是完整的、可访问的URL）
      - `mime_type`: MIME类型（必须准确对应文件类型）

   b) **可选字段**（如有则包含）：
      - `size`: 文件大小（字节）
      - `description`: 文件描述（简要说明文件用途）

请生成一个JSON格式的文件列表，格式如下：
```json
{
    "files": [
        {
            "name": "文件名",
            "type": "文件类型(document/image/video/webpage)",
            "url": "文件URL",
            "mime_type": "MIME类型",
            "size": "文件大小(字节)",
            "description": "文件描述"
        }
    ]
}

注意事项：
1. 只返回JSON格式的文件列表
2. 确保每个文件信息包含所有必要字段
3. 如果某个字段没有值，使用空字符串
4. 不要添加任何额外的说明或解释
5. 严格遵循文件类型限制
6. 只包含最终结果节点中实际存在的文件，排除中间文件
7. 如果最终结果节点没有产出文件（例如因执行失败或输出为空），返回空列表 {"files": []}
8. 禁止生成任何虚拟或推测的文件信息，必须基于节点输出中的实际数据
"""
        else:
            # Markdown汇总提示词
            prompt = base_prompt + """
重要指导：

你将接收到特定任务流中，已被识别出的"最终结果节点"的详细日志信息，包括它们的输入和输出。你的核心任务是：根据这些结果节点的产出，结合对整个任务流目标的理解，准确、完整地总结和呈现整个任务流的最终结果信息。

理解整个任务流的真正目标：

即使最终结果节点已给出，你仍需通过分析任务流的整体上下文（例如，计划名称、其他辅助信息），确定整个流程真正想要达成的最终目标。
这有助于你正确整合和解读结果节点的产出，确保最终输出的逻辑性和完整性。
最终结果内容的整合与呈现：

分析所有已识别的"最终结果节点"的输入和输出内容。
智能整合这些结果节点的输出内容，将其融合成一个连贯、有逻辑且用户友好的最终结果：
如果多个节点输出内容相关或互补，需要将它们整合为一个整体。
如果节点输出内容独立，则按逻辑顺序依次展示。
整合原则：
保持内容的完整性和准确性：不允许任何形式的精简或信息丢失。
确保逻辑连贯，避免重复：适当添加过渡语句，使内容更易理解。
按依赖顺序展示：如果节点间有依赖关系，或结果有时间顺序，请务必按正确的顺序呈现。
合并重叠内容：如果节点输出有重叠，合并相同内容，保留最完整的信息。
无解释性：不添加你自己的解释或总结，只呈现原始结果。
格式调整：如果是JSON或其他技术格式，转换为用户友好的Markdown格式，但必须保留所有数据字段和值。
链接处理：重要：原始输出中包含的任何URL或链接，必须替换为文本描述。例如：
将[点击访问结果网页](https://example.com) 替换为 "结果网页已生成，可在下方结果资源中查看"
将[下载文件](https://example.com/file.pdf) 替换为 "相关文件已生成，可在下方结果资源中查看"
将"请访问 https://example.com" 替换为 "相关资源已生成，可在下方结果资源中查看"
严禁在第二部分出现任何URL或链接。
你的回复必须使用中文，并严格遵循以下三部分结构：

第一部分 - 计划标题（必须包含）：
- 必须以"根据 **{summary_data['plan_info']['name']}**"开头
- 紧接着是"[查看计划](http://plan.ywwl.com/?plan_id={summary_data['plan_info']['plan_id']}&dag_id={summary_data['plan_info']['dag_id']}&run_id={summary_data['run_info']['dag_run_id']})"
- 这两个元素必须完全按照指定格式出现，不添加任何额外文本

第二部分 - 执行结果（必须包含）：
- 标题必须是"**最终执行结果**"
- 内容呈现：
 智能整合所有结果节点的输出内容：
 - 分析所有结果节点的输出内容，理解它们之间的关系
 - 如果多个节点输出内容相关或互补，需要将它们整合成一个连贯的结果
 - 如果节点输出内容独立，则按逻辑顺序依次展示
 - 整合原则：
   * 保持内容的完整性和准确性
   * 确保逻辑连贯，避免重复
   * 适当添加过渡语句，使内容更易理解
   * 如果节点间有依赖关系，按依赖顺序展示
   * 如果节点输出有重叠，合并相同内容，保留最完整的信息
 - 内容展示要求：
   * 将确定的最终结果节点的完整输出内容按markdown格式呈现
   * 如果是JSON或技术格式，转换为更易读的格式，但保留所有数据字段和值
   * 如果包含多部分内容，确保各部分之间的逻辑关系清晰
   * 即使内容较长，也必须完整显示
   * 重要：如果原始输出中包含任何URL或链接，请将其替换为文本描述，例如：
     - 将"[点击访问结果网页](https://example.com)" 替换为 "结果网页已生成，可在下方结果资源中查看"
     - 将"[下载文件](https://example.com/file.pdf)" 替换为 "相关文件已生成，可在下方结果资源中查看"
     - 将"请访问 https://example.com" 替换为 "相关资源已生成，可在下方结果资源中查看"
  
- 关键原则（绝对遵守）：
  - 最终结果必须基于整体任务链路分析，而非简单匹配计划名称或节点名称
  - 必须理解整个任务的真正目标，而不仅是表面描述
  - 100%保留所有结果节点的关键信息，但将所有链接替换为提示文本
  - 尽可能以markdown格式输出，格式可调整，但内容绝不精简
  - 不要说明你如何分析任务链路或如何选择最终节点
  - 禁止在最终执行结果部分出现任何URL或链接
  - 如果多个结果节点之间存在关联，必须展示它们之间的关联性
  - 如果结果节点之间存在时间顺序或依赖关系，必须按照正确的顺序展示
  - 如果某个结果节点是对其他节点结果的补充或总结，需要将其整合到相应部分

第三部分 - 输出资源列表（有则一定输出，没有则一定不要输出）：
- 标题必须是"**结果资源**"
- 重要提示：
  - 检查所有结果节点的outputs字段中是否包含文件URL、图片、视频、网页地址等资源
  - 资源类型可能包括但不限于：PDF文档、图片、视频、Office文档、网页链接等
  - 统一格式化为：[资源名称或描述](资源URL)
  - 不同类型资源使用不同子标题分类展示
  - 使用文件映射信息中的新URL替换原URL
  - 这里需要的是最终结果文件，输入的文件不要在这里展示
  - 例如：
    * **文档**：[行程安排.pdf](https://example.com/files/行程安排.pdf)
    * **图片**：[景点照片](https://example.com/images/photo.jpg)
    * **视频**：[旅游宣传片](https://example.com/videos/tour.mp4)
    * **网页**：[预订网站](https://example.com/booking)
  - 如果在任何节点中都找不到资源，则完全省略此部分
  - 重要：所有在最终执行结果中被替换为文本描述的链接，都必须在这里完整展示
  - 如果多个结果节点包含相同的资源，只展示一次，但确保使用最完整的描述

关键注意事项：
1. 必须通过整体链路分析理解任务的真正目标，不仅依赖计划名称
2. 必须先理解每个节点的作用和关系，识别真正的最终输出节点
3. 真正的最终输出不一定是最后执行的节点，而是对实现任务目标最关键的节点
4. 完整保留原始节点内容，不进行任何形式的精简或选择性保留
5. 如果内容是技术格式（如JSON），可以转换为更友好的格式，但必须保留所有信息
6. 不要添加任何不在原始节点输出中的解释或分析
7. 不要说明你是如何分析和理解任务目标的，直接展示结果
8. 所有文件链接必须使用文件映射信息中的新URL替换原URL
9. 在第二部分中不允许出现文件链接，请提示用户在第三部分查看

示例格式：
根据 **计划名称**
[查看计划](http://plan.ywwl.com/?plan_id=xxx&dag_id=xxxx&run_id=xxxx)

**最终执行结果**
根据计划执行情况，最终结果如下：

[此处是节点的完整输出内容，按适当格式展示但不删减任何信息]
[如果内容中包含文件链接，请提示用户在第三部分查看]

**结果资源**
[行程安排.pdf](https://example.com/files/行程安排.pdf)
[酒店照片](https://example.com/images/hotel.jpg)
[城市介绍](https://example.com/city)
"""

        logger.info(f"prompt:\n {prompt}")
        return prompt

    @staticmethod
    def _call_llm_generate_summary_stream(prompt: str, end_user: EndUser, conversation_id: str) -> Generator[str, None, None]:
        """流式调用大模型生成汇总

        Args:
            prompt: 提示词

        Yields:
            str: 流式输出的汇总内容
        """
        try:


            # 构造消息
            messages = [
                {"role": "system", "content": ''},
                {"role": "user", "content": prompt}
            ]

            llm = _get_llm_client(user_id=str(end_user.id), conversation_id=str(conversation_id))

            # 启用流式模式并处理分块响应
            response_stream = llm.invoke(
                messages,
                stream=False  # 关键参数：启用流式输出
            )

            for chunk in response_stream:
                content = None
                if isinstance(chunk, tuple) and len(chunk) > 1 and chunk[0] == 'content':
                    content = chunk[1]  # 元组形式
                elif hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(
                        chunk.choices[0].delta, 'content'):
                    content = chunk.choices[0].delta.content  # 兼容旧结构

                if content is not None:
                    yield content

            # 创建OpenAI客户端
            # client = OpenAI(
            #     api_key="<your api key>",
            #     base_url="http://124.223.22.249:6399/v1"
            # )
            # # 发送流式请求
            # response = client.chat.completions.create(
            #     model="deepseek-v3",
            #     messages=messages,
            #     stream=True
            # )
            #
            # #获取流式回复
            # for chunk in response:
            #     if chunk.choices[0].delta.content:
            #         yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"调用大模型生成汇总失败: {str(e)}")
            raise e

    @staticmethod
    def _process_llm_response(response_text: str) -> Tuple[str, List[Dict[str, Any]]]:
        """处理大模型返回的结果

        Args:
            response_text: 大模型返回的文本

        Returns:
            Tuple[str, List[Dict[str, Any]]]: (markdown格式的summary, 文件列表)
        """
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'```json\s*(\{[\s\S]*?\})\s*```', response_text)
            if not json_match:
                raise ValueError("未找到JSON格式的文件列表")

            # 解析JSON
            result = json.loads(json_match.group(1))

            # 验证JSON结构
            if not isinstance(result, dict):
                raise ValueError("返回结果不是有效的JSON对象")

            if "files" not in result:
                raise ValueError("返回结果缺少files字段")

            if not isinstance(result["files"], list):
                raise ValueError("files字段不是有效的数组")

            # 验证files中的必要字段
            for file_info in result["files"]:
                if not isinstance(file_info, dict):
                    raise ValueError("files数组中的元素不是有效的对象")

                required_file_fields = ["name", "type", "url"]
                for field in required_file_fields:
                    if field not in file_info:
                        raise ValueError(f"file_info中缺少必要字段: {field}")

            # 提取markdown部分（排除JSON部分）
            markdown_text = re.sub(r'```json\s*\{[\s\S]*?\}\s*```', '', response_text).strip()

            return markdown_text, result["files"]

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            logger.info(f"{response_text}")
            raise ValueError("返回结果不是有效的JSON格式")
        except Exception as e:
            logger.error(f"处理大模型返回结果失败: {str(e)}")
            raise e

    @staticmethod
    def _get_file_extension_from_mime_type(mime_type: str) -> str:
        """根据MIME类型获取文件扩展名

        Args:
            mime_type: MIME类型

        Returns:
            str: 文件扩展名
        """
        mime_type_mappings = {
            'application/pdf': 'pdf',
            'application/msword': 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'application/vnd.ms-excel': 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
            'application/vnd.ms-powerpoint': 'ppt',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
            'text/plain': 'txt',
            'image/jpeg': 'jpg',
            'image/png': 'png',
            'image/gif': 'gif'
        }
        return mime_type_mappings.get(mime_type.lower(), '')

    @staticmethod
    def _ensure_conversation_and_workbench(
            conversation_id: str,
            plan_id: str,
            end_user: EndUser
    ) -> Tuple[Conversation, Workbench]:
        """确保conversation和workbench存在，如果不存在则创建

        Args:
            conversation_id: 会话ID
            plan_id: 计划ID
            end_user: 用户信息

        Returns:
            Tuple[Conversation, Workbench]: (会话对象, 工作台对象)
        """
        try:
            # 1. 检查conversation是否存在
            conversation = db.session.query(Conversation).filter(
                Conversation.id == conversation_id,
                Conversation.is_deleted == False
            ).first()

            # 2. 如果conversation不存在，创建新的conversation
            if not conversation:
                logger.info(f"会话 {conversation_id} 不存在，创建新的会话")
                # 获取计划信息
                dag_flow = db.session.query(DagFlows).filter(
                    DagFlows.plan_id == plan_id,
                    DagFlows.is_deleted == False
                ).first()

                if not dag_flow:
                    raise ValueError(f"未找到计划: {plan_id}")

                # 创建新的conversation
                conversation = Conversation(
                    app_id=dag_flow.app_id,
                    name=dag_flow.name,
                    mode="chat",
                    status="normal",
                    from_source="system",
                    from_end_user_id=end_user.id,
                    invoke_from="web-app",
                    inputs={},
                    introduction="",
                    system_instruction="",
                    system_instruction_tokens=0,
                    dialogue_count=0
                )
                db.session.add(conversation)
                db.session.commit()

                # 更新plan中的conversation_id
                dag_flow.conversation_id = conversation.id
                db.session.commit()
                logger.info(f"已更新计划 {plan_id} 的conversation_id为 {conversation.id}")

            # 3. 检查workbench是否存在
            workbench = db.session.query(Workbench).filter(
                Workbench.conversation_id == conversation.id,
                Workbench.is_deleted == False
            ).first()

            # 4. 如果workbench不存在，创建新的workbench
            if not workbench:
                logger.info(f"工作台不存在，为会话 {conversation.id} 创建新的工作台")
                workbench = Workbench(
                    end_user_id=end_user.id,
                    conversation_id=conversation.id,
                    name=conversation.name,
                    description=f"计划 {plan_id} 的执行结果",
                    created_by=end_user.id
                )
                db.session.add(workbench)
                db.session.commit()

            return conversation, workbench

        except Exception as e:
            db.session.rollback()
            logger.error(f"确保conversation和workbench存在时出错: {str(e)}")
            raise e

    @staticmethod
    def _save_summary_to_conversation(
            conversation_id: str,
            plan_id: str,
            summary: str,
            files: List[Dict[str, Any]],
            end_user: EndUser,
            message_id: str,
            dag_run_id: str
    ) -> bool:
        """保存汇总到会话

        Args:
            conversation_id: 会话ID
            plan_id: 计划ID
            summary: 汇总内容
            files: 文件列表
            end_user: 用户信息
            message_id: 消息ID
            dag_run_id: DAG运行ID

        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保conversation和workbench存在
            conversation, workbench = PlanResultSummaryService._ensure_conversation_and_workbench(
                conversation_id=conversation_id,
                plan_id=plan_id,
                end_user=end_user
            )

            last_message = db.session.query(Message).filter(Message.conversation_id == conversation.id).order_by(
                Message.created_at.desc()).first()

            # 构建消息内容
            message_content = {
                "role": "assistant",
                "text": summary
            }

            # 创建新的消息记录
            new_message = Message(
                id=message_id,
                app_id=conversation.app_id,
                conversation_id=conversation.id,
                query=" ",
                _inputs={"run_id": dag_run_id},
                message=message_content,
                message_tokens=0,
                message_unit_price=0,
                message_price_unit=0,
                answer=message_content["text"],
                answer_tokens=0,
                answer_unit_price=0,
                answer_price_unit=0,
                provider_response_latency=0,
                total_price=0,
                currency="USD",
                status="normal",
                from_source="system",
                parent_message_id=last_message.id if last_message else None
            )

            db.session.add(new_message)
            db.session.commit()
            
            # 处理文件列表
            if files:
                logger.info(f"从汇总中提取到 {len(files)} 个文件")
                
                # 将文件保存为WorkbenchFile
                new_resources = []
                
                # 检查是否有已存在的资源URL，避免重复
                existing_resource_urls = db.session.query(WorkbenchFile.resource_url).filter(
                    WorkbenchFile.workbench_id == workbench.id,
                    WorkbenchFile.is_deleted == False,
                    WorkbenchFile.resource_url.isnot(None)
                ).all()
                existing_urls = [url[0] for url in existing_resource_urls if url[0]]
                
                for file_info in files:
                    if file_info["url"] not in existing_urls:
                        if file_info["url"] == '':
                            continue
                        if file_info["url"] is None:
                            continue
                            
                        # 获取文件类型和URL
                        file_type = file_info.get("type", "").lower()
                        original_url = file_info["url"]
                        is_webpage = file_type == "webpage"
                        
                        # 验证URL格式
                        try:
                            parsed_url = urlparse(original_url)
                            if not all([parsed_url.scheme, parsed_url.netloc]):
                                logger.warning(f"无效的URL格式: {original_url}")
                                continue
                        except Exception as e:
                            logger.error(f"URL解析失败: {str(e)}")
                            continue
                        
                        # 根据文件类型和MIME类型判断是否需要下载并保存文件
                        mime_type = file_info.get("mime_type", "").lower()
                        file_extension = PlanResultSummaryService._get_file_extension_from_mime_type(mime_type)
                        
                        # 对于网页链接或无法获取文件扩展名的URL，直接使用原始URL
                        if is_webpage or not file_extension:
                            logger.info(f"使用原始URL: {original_url} (类型: {file_type})")
                            upload_file_id = None
                            final_url = original_url
                        else:
                            try:
                                # 下载文件并保存到OSS
                                upload_file = PlanResultSummaryService._download_and_save_file(
                                    url=original_url,
                                    filename=file_info["name"],
                                    end_user=end_user
                                )
                                if upload_file:
                                    # 更新资源URL为OSS URL
                                    final_url = file_helpers.get_signed_file_url_with_type(str(upload_file.id), upload_file.mime_type, upload_file.extension)
                                    upload_file_id = upload_file.id
                                else:
                                    logger.warning(f"文件上传失败，使用原始URL: {original_url}")
                                    final_url = original_url
                                    upload_file_id = None
                            except Exception as e:
                                logger.error(f"下载并保存文件失败: {str(e)}")
                                final_url = original_url
                                upload_file_id = None

                        # 创建资源记录
                        new_resources.append(
                            WorkbenchFile(
                                workbench_id=workbench.id,
                                file_name=file_info["name"],
                                file_type='result',  # 结果类型资源
                                file_status='complete',  # 已完成状态
                                upload_file_id=upload_file_id,
                                resource_url=final_url,
                                resource_type=file_extension or file_type,  # 优先使用文件扩展名
                                resource_from='plan',
                                source_file_list=json.dumps({
                                    'dag_run_id': dag_run_id,
                                    'created_at': datetime.now(timezone.utc).isoformat(),
                                    'original_url': original_url,
                                    'mime_type': file_info.get("mime_type"),
                                    'size': file_info.get("size"),
                                    'description': file_info.get("description"),
                                    'is_webpage': is_webpage
                                }),
                                created_by=end_user.id
                            )
                        )
                
                if new_resources:
                    # 批量保存新资源
                    logger.info("添加文件到工作台")
                    db.session.bulk_save_objects(new_resources)
                    db.session.commit()
                    
                    logger.info(f"成功将 {len(new_resources)} 个资源保存到工作台文件表")

            logger.info(f"成功保存计划汇总到会话: {conversation.id}")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"保存汇总到会话失败: {str(e)}")
            return False

    @staticmethod
    def _download_and_save_file(url: str, filename: str, end_user: EndUser) -> UploadFile:
        """下载文件并保存到OSS

        Args:
            url: 文件URL
            filename: 文件名
            end_user: 用户信息

        Returns:
            UploadFile: 上传文件记录
        """
        try:
            # 下载文件
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # 获取文件内容
            content = response.content

            # 获取文件类型
            content_type = response.headers.get('content-type', '')

            # 使用FileService保存文件
            from services.file_service import FileService
            logger.info("添加文件到oss")
            upload_file = FileService.upload_file(
                filename=filename,
                content=content,
                mimetype=content_type,
                user=end_user
            )

            return upload_file

        except Exception as e:
            logger.error(f"下载并保存文件失败: {str(e)}")
            raise e

    @staticmethod
    def create_azure_llm(
            endpoint: Optional[str] = None,
            deployment_name: Optional[str] = None,
            api_key: Optional[str] = None,
            temperature: float = 0.0,
            **kwargs,
    ) -> AzureChatOpenAI:
        """
        Create an AzureOpenAIWrapper instance with Azure configuration
        """
        llm_kwargs = {
            "azure_deployment": deployment_name,
            "temperature": temperature,
            "api_version": "2025-01-01-preview",
            "streaming": True,  # 启用流式处理
            **kwargs
        }

        if endpoint:  # This will handle None or empty string
            llm_kwargs["azure_endpoint"] = endpoint

        if api_key:  # This will handle None or empty string
            llm_kwargs["api_key"] = api_key

        return AzureChatOpenAI(**llm_kwargs)

    @staticmethod
    def _is_virtual_node(result_node_id: str, nodes_dict: Dict[str, Any]) -> bool:
        """
        Check if the node corresponding to result_node_id is a virtual node.

        Args:
            result_node_id (str): The task_id of the node to check.
            nodes_dict (Dict[str, Any]): A dictionary mapping task_id to node objects.

        Returns:
            bool: True if the node exists and is virtual, False otherwise.
        """
        # Check if the result_node_id exists in nodes_dict
        if result_node_id not in nodes_dict:
            return False

        # Get the node from the dictionary
        node = nodes_dict.get(result_node_id)

        # Check if node exists and has the is_virtual attribute
        if node is None or not node.get("is_virtual"):
            return False

        # Return the value of is_virtual
        return node.get("is_virtual")

    @staticmethod
    def _identify_result_nodes_by_graph(plan_id: str, dag_id: str, dag_run_id: str) -> List[DagFlowNodeExecutions]:
        """通过分析DAG图结构识别结果节点
        
        直接从airflow_graph的dependencies.sibling_dependencies中获取task_root_end依赖的节点作为结果节点。
        这些节点是DAG中的最终输出节点。
        
        Args:
            plan_id: 计划ID
            dag_id: DAG ID
            dag_run_id: DAG运行ID
            
        Returns:
            List[DagFlowNodeExecutions]: 结果节点列表
        """
        try:
            # 1. 获取DAG Flow信息
            dag_flow = db.session.query(DagFlows).filter(
                DagFlows.plan_id == plan_id,
                DagFlows.dag_id == dag_id,
                DagFlows.is_deleted == False
            ).first()

            if not dag_flow:
                raise ValueError(f"未找到计划: {plan_id}")

            # 2. 解析airflow_graph
            airflow_graph = json.loads(dag_flow.airflow_graph) if dag_flow.airflow_graph else None
            if not airflow_graph:
                raise ValueError(f"计划 {plan_id} 没有有效的airflow_graph")

            # 4. 从sibling_dependencies中获取task_root_end依赖的节点
            result_node_ids = PlanResultSummaryService._analysis_result_node(airflow_graph)

            if not result_node_ids:
                logger.warning(f"在计划 {plan_id} 中未找到task_root_end的依赖节点")
                return []

            # 5. 获取对应的节点执行记录
            result_nodes = db.session.query(DagFlowNodeExecutions).filter(
                DagFlowNodeExecutions.dag_run_id == dag_run_id,
                DagFlowNodeExecutions.node_id.in_(result_node_ids),
                DagFlowNodeExecutions.is_deleted == False
            ).all()

            logger.info(f"通过task_root_end依赖分析找到 {len(result_nodes)} 个结果节点: {[node.node_id for node in result_nodes]}")
            return result_nodes

        except Exception as e:
            logger.error(f"通过图结构识别结果节点失败: {str(e)}")
            raise e

    @staticmethod
    def _analysis_result_node(airflow_graph: dict):
        nodes = airflow_graph.get("nodes")
        nodes_dict = {node.get("task_id"): node for node in nodes if node is not None}
        dependencies = airflow_graph.get('dependencies', {})
        sibling_dependencies = dependencies.get('sibling_dependencies', {})
        # 获取task_root_end依赖的节点列表
        result_node_ids = PlanResultSummaryService._get_result_node_ids('task_root_end', sibling_dependencies,
                                                                        nodes_dict)
        return result_node_ids

    @staticmethod
    def _get_result_node_ids(task_id: str, sibling_dependencies: Dict[str, Any], nodes_dict: [str, Any]) -> set:
        try:
            result_node_ids = set(sibling_dependencies.get(task_id, []))
            nodes_to_remove = set()
            nodes_to_add = set()

            # 首先识别需要处理的节点
            for result_node_id in result_node_ids:
                if PlanResultSummaryService._is_virtual_node(result_node_id, nodes_dict):
                    nodes_to_remove.add(result_node_id)
                    sub_result_node_ids = PlanResultSummaryService._get_result_node_ids(result_node_id, sibling_dependencies, nodes_dict)
                    if sub_result_node_ids:
                        nodes_to_add.update(sub_result_node_ids)

            # 然后进行修改操作
            result_node_ids -= nodes_to_remove
            result_node_ids.update(nodes_to_add)

            return result_node_ids
        except Exception as e:
            logger.error(e)
            raise e

    @staticmethod
    def _validate_result_node(node: DagFlowNodeExecutions, source: str) -> bool:
        """
        验证结果节点的有效性
        
        Args:
            node: 待验证的节点
            source: 节点来源 ('llm', 'graph', 'both')
            
        Returns:
            bool: 节点是否有效
        """
        try:
            # 1. 基础验证
            if not node or not node.node_id:
                return False
                
            # 2. 状态验证
            if node.status not in ['success', 'completed']:
                logger.warning(f"节点 {node.node_id} 状态异常: {node.status}")
                return False
            
            # 3. 输出验证
            if not node.outputs:
                logger.warning(f"节点 {node.node_id} 没有输出内容")
                return False
            
            try:
                outputs = json.loads(node.outputs)
                if not outputs or outputs == {}:
                    logger.warning(f"节点 {node.node_id} 输出内容为空")
                    return False
            except json.JSONDecodeError:
                logger.warning(f"节点 {node.node_id} 输出格式无效")
                return False
            
            # 4. 节点类型过滤（针对图结构识别的节点）
            # if source in ['graph', 'both']:
            #     # 过滤掉一些明显不是结果节点的类型
            #     node_name_lower = (node.node_name or '').lower()
            #     node_desc_lower = (node.node_description or '').lower()
            #
            #     # 定义需要过滤的节点类型关键词
            #     filter_keywords = [
            #         '验证', '确认', '检查', '校验', '审核', 'validate', 'verify', 'check',
            #         '测试', 'test', '调试', 'debug', '清理', 'clean', '删除', 'delete',
            #         '备份', 'backup', '日志', 'log', '监控', 'monitor'
            #     ]
            #
            #     # 检查节点名称和描述是否包含过滤关键词
            #     for keyword in filter_keywords:
            #         if keyword in node_name_lower or keyword in node_desc_lower:
            #             logger.info(f"过滤掉验证类节点: {node.node_id} ({node.node_name})")
            #             return False
            #
            # 5. 内容质量验证
            # if PlanResultSummaryService._has_meaningful_output(node):
            #     return True
            else:
                logger.warning(f"节点 {node.node_id} 输出内容质量不足")
                return False
                
        except Exception as e:
            logger.error(f"验证节点 {node.node_id if node else 'None'} 时出错: {str(e)}")
            return False
    
    @staticmethod
    def _has_meaningful_output(node: DagFlowNodeExecutions) -> bool:
        """
        检查节点输出是否包含有意义的内容
        
        Args:
            node: 节点对象
            
        Returns:
            bool: 是否包含有意义的内容
        """
        try:
            if not node.outputs:
                return False
                
            outputs = json.loads(node.outputs)
            
            # 检查是否包含文件URL
            def has_file_urls(data):
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, str) and value.startswith(('http://', 'https://')):
                            return True
                        elif isinstance(value, (dict, list)):
                            if has_file_urls(value):
                                return True
                elif isinstance(data, list):
                    for item in data:
                        if has_file_urls(item):
                            return True
                return False
            
            # 检查是否包含结构化数据
            def has_structured_data(data):
                if isinstance(data, dict):
                    # 检查是否有多个字段且不是空值
                    non_empty_fields = sum(1 for v in data.values() if v not in [None, '', [], {}])
                    return non_empty_fields >= 2
                elif isinstance(data, list):
                    return len(data) > 0
                return False
            
            # 检查是否包含文本内容
            def has_text_content(data):
                if isinstance(data, str):
                    return len(data.strip()) > 10  # 至少10个字符
                elif isinstance(data, dict):
                    for value in data.values():
                        if has_text_content(value):
                            return True
                elif isinstance(data, list):
                    for item in data:
                        if has_text_content(item):
                            return True
                return False
            
            # 满足任一条件即认为有意义
            return (has_file_urls(outputs) or 
                   has_structured_data(outputs) or 
                   has_text_content(outputs))
                   
        except Exception as e:
            logger.error(f"检查节点输出内容时出错: {str(e)}")
            return False