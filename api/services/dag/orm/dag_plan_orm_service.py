from services.dag.orm.base_orm_service import BaseORMService
from models.dag_flow import <PERSON>gPlan
from typing import List, Optional, Union
from uuid import UUID
import uuid
from datetime import datetime
from sqlalchemy import desc

class DagPlanORMService(BaseORMService[DagPlan]):
    """
    DagPlan表的ORM服务类
    提供DagPlan表的特定查询和操作方法
    """
    
    def __init__(self):
        super().__init__(DagPlan)
    
    def get_by_plan_id(self, plan_id: str) -> Optional[DagPlan]:
        """
        根据plan_id获取DAG计划
        :param plan_id: 计划ID
        :return: DagPlan实例
        """
        return DagPlan.query.filter_by(plan_id=plan_id, is_deleted=False).first()
    
    def get_by_dag_id(self, dag_id: str) -> Optional[DagPlan]:
        """
        根据dag_id获取DAG计划
        :param dag_id: DAG ID
        :return: DagPlan实例
        """
        return DagPlan.query.filter_by(dag_id=dag_id, is_deleted=False).first()
    
    def get_by_conversation_id(self, conversation_id: str) -> List[DagPlan]:
        """
        根据会话ID获取DAG计划列表
        :param conversation_id: 会话ID
        :return: DagPlan实例列表
        """
        return DagPlan.query.filter_by(conversation_id=conversation_id, is_deleted=False).all()
    
    def update_status(self, plan_id: str, status: str) -> Optional[DagPlan]:
        """
        更新DAG计划状态
        :param plan_id: 计划ID
        :param status: 新状态
        :return: 更新后的DagPlan实例
        """
        plan = self.get_by_plan_id(plan_id)
        if plan:
            plan.status = status
            plan.updated_at = datetime.utcnow()
            from extensions.ext_database import db
            db.session.commit()
        return plan
    
    def update_operate_status(self, plan_id: str, operate_status: str) -> Optional[DagPlan]:
        """
        更新DAG计划操作状态
        :param plan_id: 计划ID
        :param operate_status: 操作状态
        :return: 更新后的DagPlan实例
        """
        plan = self.get_by_plan_id(plan_id)
        if plan:
            plan.operate_status = operate_status
            plan.updated_at = datetime.utcnow()
            from extensions.ext_database import db
            db.session.commit()
        return plan
    
    def get_latest_plans(self, limit: int = 10) -> List[DagPlan]:
        """
        获取最新的DAG计划列表
        :param limit: 限制数量
        :return: DagPlan实例列表
        """
        return DagPlan.query.filter_by(is_deleted=False).order_by(desc(DagPlan.created_at)).limit(limit).all()
    
    def create_with_defaults(self, name: str, description: str, user_id: Union[str, UUID], conversation_id: str = None) -> DagPlan:
        """
        创建带有默认值的DAG计划
        :param name: 名称
        :param description: 描述
        :param user_id: 用户ID
        :param conversation_id: 会话ID
        :return: 创建的DagPlan实例
        """
        from extensions.ext_database import db
        
        plan_id = str(uuid.uuid4())
        dag_id = f"dag_{plan_id.replace('-', '_')}"
        
        plan = DagPlan(
            plan_id=plan_id,
            dag_id=dag_id,
            name=name,
            description=description,
            status='init',  # 初始状态
            operate_status='modifying',  # 默认为修改中
            conversation_id=conversation_id,
            created_by=user_id,
            updated_by=user_id
        )
        
        db.session.add(plan)
        db.session.commit()
        
        return plan
    
    def get_by_plan_ids(self, plan_ids: List[str]) -> List[DagPlan]:
        """
        根据plan_id列表批量获取DAG计划
        :param plan_ids: 计划ID列表
        :return: DagPlan实例列表
        """
        return DagPlan.query.filter(
            DagPlan.plan_id.in_(plan_ids),
            DagPlan.is_deleted == False
        ).all()
    
    def query_plans(self, filters: dict = None) -> List[DagPlan]:
        """
        按条件查询DAG计划列表
        
        Args:
            filters: 查询条件字典
                支持的条件:
                - tool_id: {"$ne": None} 表示tool_id非空
                - created_by: List[str] 创建者ID列表
                
        Returns:
            List[DagPlan]: DagPlan实例列表
        """
        query = DagPlan.query.filter_by(is_deleted=False)
        
        if filters:
            # 处理tool_id不为空的条件
            if "tool_id" in filters and filters["tool_id"].get("$ne") is None:
                query = query.filter(DagPlan.tool_id.isnot(None))
            
            # 处理created_by条件(数组)
            if "created_by" in filters:
                created_by_list = filters["created_by"]
                if isinstance(created_by_list, list) and created_by_list:
                    query = query.filter(DagPlan.created_by.in_(created_by_list))
                
        return query.all()

    def query_plans_paginated(self, page: int, page_size: int, filters: dict = None) -> tuple[List[DagPlan], int]:
        """
        分页查询DAG计划列表
        
        Args:
            page: 页码(从1开始)
            page_size: 每页大小
            filters: 查询条件字典
                支持的条件:
                - tool_id: {"$ne": None} 表示tool_id非空
                - created_by: List[str] 创建者ID列表
                - tool_ids: List[str] 工具ID列表,用于过滤特定工具
                
        Returns:
            Tuple[List[DagPlan], int]: (当前页数据列表, 总记录数)
        """
        # 如果tool_ids存在且为空，直接返回空结果
        if "tool_ids" in filters and not filters["tool_ids"]:
            return [], 0
            
        query = DagPlan.query.filter_by(is_deleted=False)
        
        if filters:
            # 处理tool_id不为空的条件
            if "tool_id" in filters and filters["tool_id"].get("$ne") is None:
                query = query.filter(DagPlan.tool_id.isnot(None))
            
            # 处理created_by条件(数组)
            if "created_by" in filters:
                created_by_list = filters["created_by"]
                if isinstance(created_by_list, list) and created_by_list:
                    query = query.filter(DagPlan.created_by.in_(created_by_list))

            # 处理tool_ids条件(数组)
            if "tool_ids" in filters:
                tool_ids_list = filters["tool_ids"]
                if isinstance(tool_ids_list, list) and tool_ids_list:
                    query = query.filter(DagPlan.tool_id.in_(tool_ids_list))

        # 计算总记录数
        total = query.count()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取分页数据
        items = query.order_by(desc(DagPlan.created_at)).offset(offset).limit(page_size).all()
        
        return items, total