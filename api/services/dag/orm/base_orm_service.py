from sqlalchemy.exc import SQLAlchemyError
from typing import List, Dict, Any, Optional, TypeVar, Generic, Type, Union
from extensions.ext_database import db
from uuid import UUID

T = TypeVar('T', bound=db.Model)

class BaseORMService(Generic[T]):
    """
    基础ORM服务类，提供通用的CRUD操作
    """
    
    def __init__(self, model_class: Type[T]):
        self.model_class = model_class
    
    def create(self, data: Dict[str, Any]) -> T:
        """
        创建记录
        :param data: 记录数据
        :return: 创建的记录
        """
        try:
            instance = self.model_class(**data)
            db.session.add(instance)
            db.session.commit()
            return instance
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def get_by_id(self, id: Union[str, UUID]) -> Optional[T]:
        """
        根据ID获取记录
        :param id: 记录ID
        :return: 记录对象，不存在则返回None
        """
        return self.model_class.query.filter_by(id=id, is_deleted=False).first()
    
    def get_all(self, **filters) -> List[T]:
        """
        获取所有记录
        :param filters: 过滤条件
        :return: 记录列表
        """
        if 'is_deleted' not in filters:
            filters['is_deleted'] = False
        return self.model_class.query.filter_by(**filters).all()
    
    def update(self, id: Union[str, UUID], data: Dict[str, Any]) -> Optional[T]:
        """
        更新记录
        :param id: 记录ID
        :param data: 更新数据
        :return: 更新后的记录，不存在则返回None
        """
        try:
            instance = self.get_by_id(id)
            if instance:
                for key, value in data.items():
                    setattr(instance, key, value)
                db.session.commit()
            return instance
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def delete(self, id: Union[str, UUID], is_soft_delete: bool = True) -> bool:
        """
        删除记录
        :param id: 记录ID
        :param is_soft_delete: 是否软删除，默认为True
        :return: 是否删除成功
        """
        try:
            instance = self.get_by_id(id)
            if not instance:
                return False
            
            if is_soft_delete:
                from datetime import datetime
                instance.is_deleted = True
                instance.deleted_at = datetime.utcnow()
                db.session.commit()
            else:
                db.session.delete(instance)
                db.session.commit()
            return True
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def batch_create(self, data_list: List[Dict[str, Any]]) -> List[T]:
        """
        批量创建记录
        :param data_list: 记录数据列表
        :return: 创建的记录列表
        """
        try:
            instances = [self.model_class(**data) for data in data_list]
            db.session.add_all(instances)
            db.session.commit()
            return instances
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def batch_update(self, update_data: List[Dict[str, Any]]) -> List[T]:
        """
        批量更新记录
        :param update_data: 更新数据列表，每个元素必须包含id字段
        :return: 更新后的记录列表
        """
        try:
            updated_instances = []
            for data in update_data:
                id_value = data.pop('id', None)
                if id_value:
                    instance = self.get_by_id(id_value)
                    if instance:
                        for key, value in data.items():
                            setattr(instance, key, value)
                        updated_instances.append(instance)
            
            if updated_instances:
                db.session.commit()
            
            return updated_instances
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
    
    def count(self, **filters) -> int:
        """
        计数查询
        :param filters: 过滤条件
        :return: 记录数量
        """
        if 'is_deleted' not in filters:
            filters['is_deleted'] = False
        return self.model_class.query.filter_by(**filters).count()
    
    def paginate(self, page: int = 1, per_page: int = 10, **filters) -> Dict[str, Any]:
        """
        分页查询
        :param page: 页码
        :param per_page: 每页数量
        :param filters: 过滤条件
        :return: 分页数据
        """
        if 'is_deleted' not in filters:
            filters['is_deleted'] = False
        
        query = self.model_class.query.filter_by(**filters)
        pagination = query.paginate(page=page, per_page=per_page)
        
        return {
            'items': pagination.items,
            'total': pagination.total,
            'page': pagination.page,
            'per_page': pagination.per_page,
            'pages': pagination.pages
        } 