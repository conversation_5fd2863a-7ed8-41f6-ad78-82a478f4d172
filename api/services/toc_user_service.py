import logging
import secrets
import uuid
from datetime import UTC, datetime, timedelta
from typing import Optional, Dict, List, Tuple
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import and_, or_

from configs import dify_config
from controllers.web.error import NotFoundError
from extensions.ext_database import db
from extensions.ext_redis import redis_client
from libs.passport import PassportService
from models import EndUser
from models.toc_user import ToCUser
from services.errors.account import AccountNotFoundError
from services.sms_service import SmsService
from services.toc_user.toc_user_settings_service import ToCUserSettingsService
from configs.remote_settings_sources.apollo import apollo_client

class TokenPair(BaseModel):
    access_token: str
    refresh_token: str


class ToCUserService:
    """Service class for ToC user operations"""
    
    sms_service = SmsService()

    @staticmethod
    def get_user_by_phone(phone: str) -> Optional[ToCUser]:
        """Get ToC user by phone number"""
        return ToCUser.query.filter(
            and_(
                ToCUser.phone == phone,
                ToCUser.is_deleted == False
            )
        ).first()

    @staticmethod
    def get_user_by_yw_user_id(yw_user_id: str) -> Optional[ToCUser]:
        """Get ToC user by yw_user_id"""
        return ToCUser.query.filter(
            and_(
                ToCUser.yw_user_id == yw_user_id,
                ToCUser.is_deleted == False
            )
        ).first()

    @staticmethod
    def create_toc_user(
                        phone: str, 
                        created_by: Optional[UUID] = None, 
                        name = None, 
                        yw_user_id = None,
                        tenant_id = None) -> ToCUser:
        """Create a new ToC user"""
        #todo 获取默认的tenants
        if not tenant_id:
            tenant_id = apollo_client.get_value(key="DEFAULT_TENANT_ID")
        # Create associated end_user first

        default_name = f"用户{phone[:3]}****{phone[-4:]}" if phone else phone

        end_user = EndUser(
            name=name if name is not None else default_name,
            type='toc_user',
            tenant_id=uuid.UUID(tenant_id),
            session_id=phone
        )
        db.session.add(end_user)
        db.session.flush()

        # Create ToC user
        toc_user = ToCUser(
            phone=phone,
            name=name if name is not None else default_name,
            end_user_id=end_user.id,
            yw_user_id=yw_user_id,
            created_by=created_by
        )
        db.session.add(toc_user)
        db.session.flush()
        
        # 初始化用户设置
        ToCUserSettingsService.create_user_settings(
            user_id=toc_user.id,
            end_user_id=end_user.id,
            created_by=created_by
        )
        
        db.session.commit()
        
        return toc_user

    @classmethod
    def send_verification_code(cls, phone: str) -> None:
        """Send verification code to phone number
        
        Args:
            phone: Phone number to send code to
            
        Raises:
            Exception: If sending fails
        """
        success, error = cls.sms_service.send_verification_code(phone)
        if not success:
            logging.error(f"发送验证码失败:{error}")
            raise Exception(f"Failed to send verification code: {error}")

    @staticmethod
    def get_user_list(page: int, limit: int, phone: str) -> dict:
        """Get paginated list of ToC users"""
        query = (ToCUser.query.filter(ToCUser.is_deleted == False).order_by(ToCUser.created_at.desc()))

        if phone:
            query = query.filter(ToCUser.phone.ilike(f"%{phone}%"))
        
        total = query.count()
        users = query.offset((page - 1) * limit).limit(limit).all()
        has_more = (page * limit) < total

        return {
            "data": [user.to_dict() for user in users],
            "total": total,
            "limit": limit,
            "has_more": has_more,
            "page": page
        }

    @staticmethod
    def disable_user(user_id: UUID, disable: bool) -> ToCUser:
        """Enable/disable a ToC user"""
        user = ToCUser.query.get(user_id)
        if not user:
            raise AccountNotFoundError()
            
        user.status = 'disabled' if disable else 'enabled'
        db.session.commit()
        return user

    @staticmethod
    def delete_user(user_id: UUID) -> None:
        """Soft delete a ToC user"""
        user = ToCUser.query.get(user_id)
        if not user:
            raise AccountNotFoundError()
            
        user.is_deleted = True

        end_user = db.session.query(EndUser).filter(EndUser.id == user.end_user_id).first()

        db.session.delete(end_user)
        db.session.commit()

    @staticmethod
    def get_user_jwt_token(toc_user: ToCUser) -> str:
        """Generate JWT token for ToC user"""
        exp_dt = datetime.now(UTC) + timedelta(minutes=dify_config.ACCESS_TOKEN_EXPIRE_MINUTES)
        exp = int(exp_dt.timestamp())
        
        payload = {
            "user_id": str(toc_user.id),
            "end_user_id": str(toc_user.end_user_id),
            "type": "toc_user",
            "exp": exp,
            "iss": dify_config.EDITION,
            "sub": "ToC API Passport"
        }
        
        token: str = PassportService().issue(payload)
        return token

    @staticmethod
    def _generate_refresh_token(length: int = 64) -> str:
        """Generate a random refresh token"""
        return secrets.token_hex(length)

    @staticmethod
    def _get_refresh_token_key(refresh_token: str) -> str:
        """Get Redis key for refresh token"""
        return f"toc_refresh_token:{refresh_token}"

    @staticmethod
    def _get_user_refresh_token_key(user_id: str) -> str:
        """Get Redis key for user's refresh token"""
        return f"toc_user_refresh_token:{user_id}"

    @staticmethod
    def _store_refresh_token(refresh_token: str, user_id: str) -> None:
        """Store refresh token in Redis"""
        expiry = timedelta(days=dify_config.REFRESH_TOKEN_EXPIRE_DAYS)
        redis_client.setex(
            ToCUserService._get_refresh_token_key(refresh_token), 
            expiry,
            user_id
        )
        redis_client.setex(
            ToCUserService._get_user_refresh_token_key(user_id),
            expiry,
            refresh_token
        )

    @staticmethod
    def _delete_refresh_token(refresh_token: str, user_id: str) -> None:
        """Delete refresh token from Redis"""
        redis_client.delete(ToCUserService._get_refresh_token_key(refresh_token))
        redis_client.delete(ToCUserService._get_user_refresh_token_key(user_id))

    @classmethod
    def login(cls, user: ToCUser, ip_address: Optional[str] = None) -> TokenPair:
        """Login ToC user and generate token pair"""
        # Generate tokens
        access_token = cls.get_user_jwt_token(user)
        refresh_token = cls._generate_refresh_token()

        # Store refresh token
        cls._store_refresh_token(refresh_token, str(user.id))

        return TokenPair(
            access_token=access_token,
            refresh_token=refresh_token
        )

    @classmethod
    def refresh_token(cls, refresh_token: str) -> TokenPair:
        """Refresh token pair using refresh token"""
        # Verify the refresh token
        user_id = redis_client.get(cls._get_refresh_token_key(refresh_token))
        if not user_id:
            raise ValueError("Invalid refresh token")

        # Get user
        user = ToCUser.query.get(user_id.decode("utf-8"))
        if not user or user.is_deleted or user.status == 'disabled':
            raise ValueError("Invalid or disabled user")

        # Generate new tokens
        new_access_token = cls.get_user_jwt_token(user)
        new_refresh_token = cls._generate_refresh_token()

        # Replace old refresh token
        cls._delete_refresh_token(refresh_token, str(user.id))
        cls._store_refresh_token(new_refresh_token, str(user.id))

        return TokenPair(
            access_token=new_access_token,
            refresh_token=new_refresh_token
        )

    @classmethod
    def logout(cls, user: ToCUser) -> None:
        """Logout ToC user"""
        refresh_token = redis_client.get(cls._get_user_refresh_token_key(str(user.id)))
        if refresh_token:
            cls._delete_refresh_token(refresh_token.decode("utf-8"), str(user.id))

    @staticmethod
    def get_user_by_end_user_id(end_user_id: UUID) -> Optional[ToCUser]:
        """获取通过end_user_id查询ToC用户

        Args:
            end_user_id: 关联的end_user_id

        Returns:
            Optional[ToCUser]: 查询到的用户信息，如果不存在则返回None
        """
        return ToCUser.query.filter(
            and_(
                ToCUser.end_user_id == end_user_id,
                ToCUser.is_deleted == False
            )
        ).first()

    @staticmethod
    def get_user_by_id(user_id: UUID) -> Optional[ToCUser]:
        """获取通过end_user_id查询ToC用户

        Args:
            end_user_id: 关联的end_user_id

        Returns:
            Optional[ToCUser]: 查询到的用户信息，如果不存在则返回None
        """
        return ToCUser.query.filter(
            and_(
                ToCUser.id == user_id,
                ToCUser.is_deleted == False
            )
        ).first()

    @staticmethod
    def get_users_by_ids(user_ids: List[str]) -> List[ToCUser]:
        """批量获取ToC用户信息

        Args:
            user_ids: 用户ID列表

        Returns:
            List[ToCUser]: 用户信息列表
        """
        return ToCUser.query.filter(
            and_(
                ToCUser.id.in_(user_ids),
                ToCUser.is_deleted == False
            )
        ).all()

    @classmethod
    def verify_phone_code(cls, phone: str, code: str) -> bool:
        """
        Verify phone verification code
        
        Args:
            phone: Phone number
            code: Verification code to check
            
        Returns:
            bool: True if code is valid
        """
        test_login_code = apollo_client.get_value("TEST_LOGIN_CODE")
        # 万能验证码
        if test_login_code and test_login_code != "":
            return True
        return cls.sms_service.verify_code(phone, code)

    @classmethod
    def search_members(
        cls,
        end_user: EndUser,
        tenant_id: str,
        name: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Tuple[List[Dict], bool, int]:
        """
        搜索企业租户下的成员列表
        
        Args:
            tenant_id: 租户ID
            name: 搜索关键词（可选）
            page: 页码，从1开始
            limit: 每页数量
            
        Returns:
            Tuple[List[Dict], bool, int]: (成员列表, 是否有更多, 总数)
        """

        if not tenant_id:
            tenant_id = end_user.tenant_id
            #tenant_id = apollo_client.config.get('TENANT_ID')

        try:
            # 构建基础查询，通过 EndUser 表关联查询租户信息
            query = (
                db.session.query(ToCUser, EndUser)
                .join(
                    EndUser,
                    ToCUser.end_user_id == EndUser.id
                )
                .filter(
                    EndUser.tenant_id == tenant_id,
                    ToCUser.is_deleted == False
                )
            )
            
            # 添加名称搜索条件
            if name:
                query = query.filter(
                    ToCUser.name.ilike(f'%{name}%')
                )
            
            # 计算总数
            total = query.count()
            
            # 分页
            offset = (page - 1) * limit
            members = query.order_by(ToCUser.name).offset(offset).limit(limit).all()
            
            # 转换为字典列表
            member_list = [{
                'id': str(member.ToCUser.end_user_id),
                'name': member.ToCUser.name,
                'avatar': member.ToCUser.avatar,
                'status': member.ToCUser.status,
                'tenant_id': str(member.EndUser.tenant_id)
            } for member in members]
            
            # 判断是否有更多数据
            has_more = total > offset + len(members)
            
            return member_list, has_more, total
            
        except Exception as e:
            raise NotFoundError(f"Failed to search members: {str(e)}")