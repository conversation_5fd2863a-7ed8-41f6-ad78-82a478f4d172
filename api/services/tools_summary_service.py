import datetime
import json
from typing import List, Optional, Union, Dict, Any

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from configs.app_config import logger
from core.tools.tool_manager import ToolManager
from models import db
from models.tools import ApiToolProvider, BuiltinToolProvider, ToolsSummary, WorkflowToolProvider
from services.tools.tools_transform_service import ToolTransformService


class ToolsSummaryService:
    """
    工具汇总服务，用于同步和管理三种不同类型的工具数据
    """

    def __init__(self, db_session: Union[Session, AsyncSession]):
        """
        初始化工具汇总服务
        
        Args:
            db_session: 数据库会话，可以是同步或异步会话
        """
        self.db_session = db_session
        self.is_async = isinstance(db_session, AsyncSession)

    def _extract_label_from_entity(self, entity) -> str:
        """
        从工具实体中提取标签信息
        
        Args:
            entity: 工具实体
            
        Returns:
            提取的标签信息
        """
        tool_label = ""
        if entity.identity.label:
            label_data = entity.identity.label.to_dict()
            if isinstance(label_data, dict):
                # 优先使用zh_Hans
                if 'zh_Hans' in label_data:
                    tool_label = label_data['zh_Hans']
                # 其次使用en_US
                elif 'en_US' in label_data:
                    tool_label = label_data['en_US']
        return tool_label

    def sync_builtin_tool(self, provider: BuiltinToolProvider) -> List[ToolsSummary]:
        """
        同步内置工具到工具汇总表
        :param provider: 内置工具提供者
        :return: 同步的工具列表
        """
        logger.info(f"开始同步内置工具: {provider.provider}")
        try:
            # 获取 provider 下的所有工具
            provider_controller = ToolManager.get_builtin_provider(provider.provider, provider.tenant_id)
            tools = provider_controller.get_tools()
            
            result = []
            for tool_entity in tools:
                try:
                    # 检查工具是否已存在
                    if self.is_async:
                        stmt = select(ToolsSummary).where(
                            and_(
                                ToolsSummary.tool_name == tool_entity.entity.identity.name,
                                ToolsSummary.provider == provider.provider,
                                ToolsSummary.tool_type == "builtin",
                                ToolsSummary.tenant_id == provider.tenant_id
                            )
                        )
                        result_set = self.db_session.execute(stmt)
                        existing_tool = result_set.scalar_one_or_none()
                    else:
                        existing_tool = self.db_session.query(ToolsSummary).filter(
                            ToolsSummary.tool_name == tool_entity.entity.identity.name,
                            ToolsSummary.provider == provider.provider,
                            ToolsSummary.tool_type == "builtin",
                            ToolsSummary.tenant_id == provider.tenant_id
                        ).first()
                    
                    # 提取标签信息
                    tool_label = self._extract_label_from_entity(tool_entity.entity)
                    
                    # 准备工具数据
                    tool_data = {
                        'tool_name': tool_entity.entity.identity.name,
                        'tool_type': "builtin",
                        'provider': provider.provider,
                        'tenant_id': provider.tenant_id,
                        'created_by': provider.user_id,
                        "tool_description": self.extract_preferred_description(
                            i18n_object=tool_entity.entity.description.human, preferred_languages=['zh_Hans', 'en_US']),
                        'tool_parameters': json.dumps([{
                            'name': param.name,
                            'type': param.type.value,
                            'required': param.required,
                            'label': self.extract_preferred_description(i18n_object=param.label, preferred_languages= ['zh_Hans', 'en_US']) if param.label else {},
                            'description': self.extract_preferred_description(i18n_object=param.human_description, preferred_languages= ['zh_Hans', 'en_US']) if param.human_description else {},
                            'llm_description': param.llm_description
                        } for param in tool_entity.entity.parameters], ensure_ascii=False) if tool_entity.entity.parameters else json.dumps([], ensure_ascii=False),
                        'updated_at' : datetime.datetime.now(datetime.UTC).replace(tzinfo=None),
                        'created_at': datetime.datetime.now(datetime.UTC).replace(tzinfo=None),
                        'tool_label': tool_label
                    }
                    
                    if existing_tool:
                        # 更新现有工具
                        logger.info(f"更新内置工具: {tool_entity.entity.identity.name}")
                        for key, value in tool_data.items():
                            setattr(existing_tool, key, value)
                        result.append(existing_tool)
                    else:
                        # 创建新工具
                        logger.info(f"创建新内置工具: {tool_entity.entity.identity.name}")
                        new_tool = ToolsSummary(**tool_data)
                        self.db_session.add(new_tool)
                        result.append(new_tool)
                except Exception as e:
                    logger.error(f"处理内置工具 {tool_entity.entity.identity.name} 时出错: {str(e)}", exc_info=True)
            
            # 确保所有更改都已提交到数据库
            self.db_session.commit()
            self.db_session.flush()

            
            logger.info(f"内置工具同步完成: {provider.provider}, 共 {len(result)} 个工具")
            return result
        except Exception as e:
            # 发生错误时回滚事务
            self.db_session.rollback()
            logger.error(f"同步内置工具 {provider.provider} 时出错: {str(e)}", exc_info=True)
            raise

    def sync_api_tool(self, tenant_id: str, provider: ApiToolProvider) -> List[ToolsSummary]:
        """
        同步API工具到工具汇总表
        :param tenant_id: 租户ID
        :param provider: API工具提供者
        :return: 同步的工具列表
        """
        logger.info(f"开始同步API工具: {provider.name}")
        result = []
        try:
            # 获取 provider 下的所有工具
            provider_controller = ToolTransformService.api_provider_to_controller(provider)
            tools = provider_controller.get_tools(tenant_id=tenant_id)
            
            for tool_entity in tools:
                try:
                    # 检查工具是否已存在
                    if self.is_async:
                        stmt = select(ToolsSummary).where(
                            and_(
                                ToolsSummary.tool_name == tool_entity.entity.identity.name,
                                ToolsSummary.provider == provider.id,
                                ToolsSummary.tool_type == "api",
                                ToolsSummary.tenant_id == tenant_id
                            )
                        )
                        result_set = self.db_session.execute(stmt)
                        existing_tool = result_set.scalar_one_or_none()
                    else:
                        existing_tool = self.db_session.query(ToolsSummary).filter(
                            ToolsSummary.tool_name == tool_entity.entity.identity.name,
                            ToolsSummary.provider == provider.id,
                            ToolsSummary.tool_type == "api",
                            ToolsSummary.tenant_id == tenant_id
                        ).first()
                    
                    # 提取标签信息
                    tool_label = self._extract_label_from_entity(tool_entity.entity)
                    
                    # 准备工具数据
                    tool_data = {
                        'tool_name': tool_entity.entity.identity.name,
                        'tool_type': "api",
                        'provider': provider.id,
                        'tenant_id': tenant_id,
                        'created_by': provider.user_id,
                        "tool_description": self.extract_preferred_description(
                            i18n_object=tool_entity.entity.description.human, preferred_languages=['zh_Hans', 'en_US']),
                        'tool_parameters': json.dumps([{
                            'name': param.name,
                            'type': param.type.value,
                            'required': param.required,
                            'label': self.extract_preferred_description(i18n_object=param.label, preferred_languages=['zh_Hans', 'en_US']) if param.label else {},
                            'description': self.extract_preferred_description(i18n_object=param.human_description, preferred_languages=['zh_Hans', 'en_US']) if param.human_description else {},
                            'llm_description': param.llm_description
                        } for param in tool_entity.entity.parameters], ensure_ascii=False) if tool_entity.entity.parameters else json.dumps([], ensure_ascii=False),
                        'updated_at': datetime.datetime.now(datetime.UTC).replace(tzinfo=None),
                        'created_at': datetime.datetime.now(datetime.UTC).replace(tzinfo=None),
                        'tool_label': tool_label
                    }
                    
                    if existing_tool:
                        # 更新现有工具
                        logger.info(f"更新API工具: {tool_entity.entity.identity.name}")
                        for key, value in tool_data.items():
                            setattr(existing_tool, key, value)
                        result.append(existing_tool)
                    else:
                        # 创建新工具
                        logger.info(f"创建新API工具: {tool_entity.entity.identity.name}")
                        new_tool = ToolsSummary(**tool_data)
                        self.db_session.add(new_tool)
                        result.append(new_tool)

                    # 立即提交当前工具的更改
                    self.db_session.commit()
                    self.db_session.flush()

                    
                except Exception as e:
                    # 发生错误时回滚当前工具的事务
                    self.db_session.rollback()
                    logger.error(f"处理API工具 {tool_entity.entity.identity.name} 时出错: {str(e)}", exc_info=True)
                    continue
            
            logger.info(f"API工具同步完成: {provider.name}, 共 {len(result)} 个工具")
            return result
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"同步API工具 {provider.name} 时出错: {str(e)}", exc_info=True)
            raise

    def sync_workflow_tool(self, tool: WorkflowToolProvider) -> ToolsSummary:
        """
        同步工作流工具到工具汇总表
        :param tool: 工作流工具提供者
        :return: 同步后的工具
        """
        logger.info(f"开始同步工作流工具: {tool.name}")
        try:
            # 检查工具是否已存在
            if self.is_async:
                stmt = select(ToolsSummary).where(
                    and_(
                        ToolsSummary.tool_id == tool.id,
                        ToolsSummary.tool_type == "workflow",
                        ToolsSummary.tenant_id == tool.tenant_id
                    )
                )
                result_set = self.db_session.execute(stmt)
                existing_tool = result_set.scalar_one_or_none()
            else:
                existing_tool = self.db_session.query(ToolsSummary).filter(
                    ToolsSummary.tool_id == tool.id,
                    ToolsSummary.tool_type == "workflow",
                    ToolsSummary.tenant_id == tool.tenant_id
                ).first()
            
            # 准备工具数据
            tool_data = {
                'tool_id': tool.id,
                'tool_name': tool.name,
                'tool_type': "workflow",
                'provider': tool.app_id,
                'tenant_id': tool.tenant_id,
                'created_by': tool.user_id,
                'tool_description': tool.description,
                'tool_parameters':tool.parameter_configuration,
                'tool_label': tool.label,
                'updated_at' : datetime.datetime.now(datetime.UTC).replace(tzinfo=None),
                'created_at' : datetime.datetime.now(datetime.UTC).replace(tzinfo=None)

            }
            
            if existing_tool:
                # 更新现有工具
                logger.info(f"更新工作流工具: {tool.name}")
                for key, value in tool_data.items():
                    setattr(existing_tool, key, value)
                result = existing_tool
            else:
                # 创建新工具
                logger.info(f"创建新工作流工具: {tool.name}")
                new_tool = ToolsSummary(**tool_data)

                self.db_session.add(new_tool)
                result = new_tool
            
            # 如果是异步会话，确保所有更改都已刷新到数据库
            self.db_session.commit()
            self.db_session.flush()

            
            logger.info(f"工作流工具同步完成: {tool.name}")
            return result
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"同步工作流工具 {tool.name} 时出错: {str(e)}", exc_info=True)
            raise

    def delete_tool_summary(self, tool_id: str, tool_type: str) -> bool:
        """
        删除工具汇总记录
        
        Args:
            tool_id: 工具ID
            tool_type: 工具类型
            
        Returns:
            bool: 是否删除成功
        """
        logger.info(f"开始同步工作流工具 delete: {tool_id}, {tool_type}")

        try:
            if tool_type == "workflow":

                tool = self.db_session.query(ToolsSummary).filter(
                    ToolsSummary.tool_id == tool_id,
                    ToolsSummary.tool_type == tool_type
                ).first()
                if tool:
                    self.db_session.delete(tool)
                    self.db_session.commit()
                return True
                
            elif tool_type == "builtin":

                tools = self.db_session.query(ToolsSummary).filter(
                    ToolsSummary.provider == tool_id,
                    ToolsSummary.tool_type == tool_type
                ).all()
                for tool in tools:
                    self.db_session.delete(tool)
                self.db_session.commit()
                return True

            elif tool_type == "api":
                # 获取provider信息

                provider = self.db_session.query(ApiToolProvider).filter(
                    ApiToolProvider.id == tool_id
                ).first()
                
                if provider:
                    # 删除该provider下的所有工具记录

                    tools = self.db_session.query(ToolsSummary).filter(
                        ToolsSummary.provider == provider.name,
                        ToolsSummary.tool_type == tool_type
                    ).all()
                    for tool in tools:
                        self.db_session.delete(tool)
                    self.db_session.commit()
                return True
            
            return False
        except Exception as e:
            logger.error(f"删除工具汇总记录失败: {str(e)}")
            return False

    async def get_tool_summary(self, tool_id: str, tool_type: str) -> Optional[ToolsSummary]:
        """
        获取工具汇总记录
        
        Args:
            tool_id: 工具ID
            tool_type: 工具类型
            
        Returns:
            Optional[ToolsSummary]: 工具汇总记录
        """
        if self.is_async:
            stmt = select(ToolsSummary).where(
                ToolsSummary.tool_id == tool_id,
                ToolsSummary.tool_type == tool_type
            )
            result = await self.db_session.execute(stmt)
            return result.scalar_one_or_none()
        else:
            return self.db_session.query(ToolsSummary).filter(
                ToolsSummary.tool_id == tool_id,
                ToolsSummary.tool_type == tool_type
            ).first()
            
    async def list_all_tools(self, tenant_id: Optional[str] = None) -> List[ToolsSummary]:
        """
        获取所有工具汇总记录
        
        Args:
            tenant_id: 租户ID，如果提供则只返回该租户的工具
            
        Returns:
            List[ToolsSummary]: 工具汇总记录列表
        """
        if self.is_async:
            if tenant_id:
                stmt = select(ToolsSummary).where(ToolsSummary.tenant_id == tenant_id)
            else:
                stmt = select(ToolsSummary)
            result = await self.db_session.execute(stmt)
            return result.scalars().all()
        else:
            if tenant_id:
                return self.db_session.query(ToolsSummary).filter(
                    ToolsSummary.tenant_id == tenant_id
                ).all()
            else:
                return self.db_session.query(ToolsSummary).all()
                
    def delete_plugin_tools(self, tenant_id: str, plugin_id: str) -> bool:
        """
        删除指定插件的所有工具汇总记录
        
        Args:
            tenant_id: 租户ID
            plugin_id: 插件ID
            
        Returns:
            bool: 是否删除成功
        """
        logger.info(f"开始删除插件 {plugin_id} 的工具汇总记录")
        try:
            # 获取所有插件提供者
            providers = ToolManager.list_plugin_providers(tenant_id)
            
            # 过滤出当前插件的提供者
            plugin_providers = [p for p in providers if p.plugin_id == plugin_id]
            
            # 删除每个提供者的工具记录
            for provider in plugin_providers:
                try:
                    self.delete_tool_summary(str(provider.entity.identity.name), "builtin")
                    logger.info(f"已删除插件 {plugin_id} 提供的工具: {provider.entity.identity.name}")
                except Exception as e:
                    logger.error(f"删除工具记录时出错: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"删除插件工具汇总记录时出错: {str(e)}")
            return False

    @staticmethod
    def extract_preferred_description(
            i18n_object: Optional[Any],
            preferred_languages: List[str],
            default_description: Optional[str] = None
    ) -> Optional[str]:
        """
        从包含 i18n 描述的对象中提取首选语言的描述字符串。

        该函数假定 i18n_object 有一个 .to_dict() 方法，
        返回一个类似 {'lang_code': 'description_text', ...} 的字典。

        Args:
            i18n_object: 包含 .to_dict() 方法的 i18n 对象，或者 None。
            preferred_languages: 一个包含语言代码字符串的列表，按优先级排序。
                                 例如: ['zh_Hans', 'en_US']
            default_description: 如果找不到任何有效的描述，则返回此默认值。

        Returns:
            找到的首选描述字符串，或者 default_description。
        """
        if i18n_object is None:
            return default_description

        descriptions_dict: Optional[Dict[str, str]] = None
        try:
            # 假设对象有 .to_dict() 方法
            descriptions_dict = i18n_object.to_dict()
        except AttributeError:
            logger.warning(f"对象 {type(i18n_object)} 没有 'to_dict' 方法。")
            return default_description
        except Exception as e:
            logger.error(f"调用 .to_dict() 时出错: {e}", exc_info=True)  # exc_info=True 会记录堆栈跟踪
            return default_description

        if not descriptions_dict:  # 检查是否为 None 或空字典
            return default_description

        # 1. 按优先级列表查找
        for lang_code in preferred_languages:
            description = descriptions_dict.get(lang_code)
            if description:  # 检查是否为 None 或空字符串
                return description  # 找到第一个非空的就返回

        # 2. 如果优先级列表中的语言都没有找到，尝试查找字典中存在的第一个非空描述作为备选
        logger.debug(f"在 {preferred_languages} 中未找到描述，尝试备选方案...")  # 使用 debug 级别记录详细信息
        for description in descriptions_dict.values():
            if description:  # 找到任何一个非空的就返回
                return description

        # 3. 如果完全没有任何非空描述
        logger.debug("在 i18n 对象中未找到任何有效的描述。")
        return default_description

    def get_enabled_tools(self) -> list[Dict[str, str]]:
        query = db.session.query(ToolsSummary).filter(
            ToolsSummary.enabled == True
        )
        tools = query.all()

        result = []

        for tool in tools:
            tool_data = {
                "id": str(tool.id),
                "name": tool.tool_name,
                "description": tool.tool_description,
                "parameters": tool.tool_parameters,
                "label": tool.tool_label,
                "type": tool.tool_type,
                "execution_mode": tool.execution_mode,
            }
            if tool.tool_configuration:
                tool_data["configuration"] = tool.tool_configuration
            result.append(tool_data)

        return result