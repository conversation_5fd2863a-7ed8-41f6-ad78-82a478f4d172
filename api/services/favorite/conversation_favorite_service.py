from typing import Optional, List, Dict, Any

from sqlalchemy import and_

from extensions.ext_database import db
from models.conversation_favorite import ConversationFavorite
from models.toc_user import ToCUser
from services.dashboard.dashboard_service import BusinessDashboardService


class ConversationFavoriteService:
    """会话收藏服务类"""
    
    VALID_FAVORITE_TYPES = ['dashboard', 'agent', 'tool', 'webpage']
    
    @staticmethod
    def get_favorites(
        conversation_id: str,
        toc_user: ToCUser,
        favorite_type: Optional[str] = None
    ) -> List[ConversationFavorite]:
        """获取会话的收藏列表
        
        Args:
            conversation_id: 会话ID
            toc_user: 用户
            favorite_type: 收藏类型，可选
            
        Returns:
            收藏对象列表
        """
        query = ConversationFavorite.query.filter(
            ConversationFavorite.conversation_id == conversation_id,
            ConversationFavorite.end_user_id == toc_user.end_user_id,
        )
        
        if favorite_type:
            query = query.filter(ConversationFavorite.favorite_type == favorite_type)
            
        return query.order_by(ConversationFavorite.created_at.desc()).all()
    
    @staticmethod
    def _get_target_info(target_id: Optional[str] = None, target_url: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """根据类型和ID/URL获取目标详细信息
        
        Args:
            target_id: 目标ID
            target_url: 目标URL
            
        Returns:
            目标详细信息，如果未找到则返回None
            
        Raises:
            ValueError: 当参数无效时
        """
        if not target_id and not target_url:
            raise ValueError("target_id 和 target_url 不能同时为空")
            
            # 从看板服务获取信息
        if target_url:
            # 通过URL查找看板
            dashboard = BusinessDashboardService.get_dashboard(
                dashboard_url=target_url,
            )
            if dashboard and dashboard.dashboard_url == target_url:
                return {
                    'target_id': str(dashboard.id),
                    'favorite_type': 'dashboard',
                    'target_name': dashboard.name,
                    'target_url': dashboard.dashboard_url,
                    'description': dashboard.description,
                    'favorite_metadata': {
                        'business_domain_id': dashboard.business_domain_id,
                        'enable': dashboard.enable
                    }
                }
            else:
                return {
                    'target_id': None,
                    'favorite_type' : 'webpage',
                    'target_name': target_url,
                    'target_url': target_url,
                    'description': None,
                    'favorite_metadata': None
                }

        if target_id:
            # TODO: 实现Agent信息获取逻辑
            # TODO: 实现Tool信息获取逻辑

            pass
            
        return None
    
    @staticmethod
    def add_favorite(
        conversation_id: str,
        target_name: Optional[str] = None,
        favorite_type: Optional[str] = None,
        target_id: Optional[str] = None,
        target_url: Optional[str] = None,
        end_user_id: str = None,
        created_by: Optional[str] = None
    ) -> ConversationFavorite:
        """添加收藏
        
        Args:
            conversation_id: 会话ID
            favorite_type: 收藏类型
            target_id: 目标ID
            target_url: 目标URL
            end_user_id: 终端用户ID
            created_by: 创建者ID
            
        Returns:
            新创建的收藏对象
        
        Raises:
            ValueError: 当收藏类型无效、重复收藏或目标不存在时
        """
        # 检查是否重复收藏
        existing = ConversationFavorite.query.filter(
            and_(
                ConversationFavorite.conversation_id == conversation_id,
                ConversationFavorite.target_id == target_id if target_id else ConversationFavorite.target_url == target_url
            )
        ).first()
        
        if existing:
            raise ValueError("请勿重复添加")

        favorites = ConversationFavorite.query.filter(
            and_(
                ConversationFavorite.conversation_id == conversation_id
            )
        ).all()

        if len(favorites) >= 20:
            raise ValueError("已达上限，请先移除部分再添加")
            
        # 获取目标详细信息
        target_info = ConversationFavoriteService._get_target_info(
            target_id=target_id,
            target_url=target_url
        )
        
        if not target_info:
            raise ValueError("未找到目标信息")
            
        # 创建新收藏
        favorite = ConversationFavorite(
            conversation_id=conversation_id,
            favorite_type=target_info['favorite_type'],
            target_id=target_info['target_id'] or target_id,
            target_url=target_info['target_url'] or target_url,
            target_name=target_name if target_name else target_info['target_name'],
            description=target_info['description'],
            favorite_metadata=target_info['favorite_metadata'],
            end_user_id=end_user_id,
            created_by=created_by
        )
        
        db.session.add(favorite)
        db.session.commit()
        
        return favorite
    
    @staticmethod
    def update_favorite(
        conversation_id: str,
        favorite_id: str,
        target_name: Optional[str] = None,
        updated_by: Optional[str] = None
    ) -> Optional[ConversationFavorite]:
        """更新收藏信息
        
        Args:
            conversation_id: 会话ID
            favorite_id: 收藏ID
            target_name: 新的目标名称
            description: 新的描述
            updated_by: 更新者ID
            
        Returns:
            更新后的收藏对象，如果不存在则返回None
        """
        favorite = ConversationFavorite.query.filter(
            and_(
                ConversationFavorite.id == favorite_id,
                ConversationFavorite.conversation_id == conversation_id
            )
        ).first()
        
        if not favorite:
            return None
            
        if target_name is not None:
            favorite.target_name = target_name

        if updated_by:
            favorite.updated_by = updated_by
            
        db.session.commit()
        
        return favorite
    
    @staticmethod
    def remove_favorite(
        conversation_id: str,
        favorite_id: str
    ) -> bool:
        """移除收藏
        
        Args:
            conversation_id: 会话ID
            favorite_id: 收藏ID
            
        Returns:
            是否成功移除
        """
        favorite = ConversationFavorite.query.filter(
            and_(
                ConversationFavorite.id == favorite_id,
                ConversationFavorite.conversation_id == conversation_id
            )
        ).first()
        
        if favorite:
            db.session.delete(favorite)
            db.session.commit()
            return True
            
        return False 