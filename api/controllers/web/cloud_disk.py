import uuid
from typing import Any, Dict

from flask import current_app, request
from flask_login import current_user
from flask_restful import Resource, reqparse

from core.tools.tool_file_manager import ToolFileManager
from extensions.ext_database import db

from controllers.web import api
from controllers.web.error import (
    CloudDiskError,
    CloudDiskFolderNotFoundError,
    CloudDiskInvalidParameterError,
    CloudDiskOperationError,
)
from core.file import helpers as file_helpers
from libs.toc_login import toc_login_required
from models import ToolFile
from models.cloud_disk import CloudDisk
from models.toc_user import ToCUser

# from controllers.console.app.error import AppError, CloudDiskError, CloudDiskInvalidParameterError, CloudDiskFolderNotFoundError, CloudDiskOperationError
from services.cloud_disk.cloud_disk_service import CloudDiskService


class CloudDiskListAPI(Resource):
    """云盘文件列表 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def get(self, toc_user: ToCUser) -> Dict[str, Any]:
        """获取云盘文件列表（共享或个人）
        
        URL参数:
            parent_id: 父文件夹ID
            name: 文件名称关键词，用于模糊搜索（可选）
            page: 页码，默认为1
            limit: 每页数量，默认为20
        
        Returns:
            Dict[str, Any]: {
                'data': [...],  # 文件列表
                'total': int,   # 总记录数
                'page': int,    # 当前页码
                'limit': int,   # 每页数量
                'pages': int    # 总页数
            }
        """
        try:
            # 获取请求参数
            # disk_type = request.args.get('type', 'personal')
            parent_id_str = request.args.get('parent_id')
            name = request.args.get('name', '').strip()
            
            # 获取分页参数
            try:
                page = max(1, int(request.args.get('page', 1)))
                limit = max(1, min(50, int(request.args.get('limit', 20))))  # 限制最大为50
            except ValueError:
                raise CloudDiskInvalidParameterError("无效的分页参数")

            parent_id = None
            if parent_id_str:
                try:
                    parent_id = uuid.UUID(parent_id_str)
                except ValueError:
                    raise CloudDiskInvalidParameterError("无效的父文件夹ID")

            result = self.service.get_file_list(toc_user, parent_id, page, limit, name)
            
            return {
                'data': result,
                'result': 'success'
            }, 200
            
        except CloudDiskError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"获取云盘文件列表失败: {str(e)}")
            return {
                'result': 'fail',
                'message': "获取文件列表失败"
            }, 500


class CloudDiskFolderAPI(Resource):
    """云盘文件夹管理 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """创建文件夹
        
        JSON参数:
            parent_id: 父文件夹ID（可选，如果为空则创建在根目录下）
            name: 文件夹名称
        
        Returns:
            Dict[str, Any]: 新创建的文件夹ID

        Raises:
            CloudDiskInvalidParameterError: 当文件夹名称为空或参数无效时
            CloudDiskFolderNotFoundError: 当指定的父文件夹不存在时
            CloudDiskOperationError: 当创建文件夹失败时
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            name = data.get('name')
            if not name:
                return {
                    'error': 'invalid_parameter',
                    'description': '文件夹名称不能为空'
                }, 400
            
            parent_id_str = data.get('parent_id')
            parent_id = None

            if parent_id_str:
                try:
                    parent_id = uuid.UUID(parent_id_str)
                except ValueError:
                    raise CloudDiskInvalidParameterError("无效的父文件夹ID")

                # 验证指定的父文件夹是否存在
                parent_folder = CloudDisk.query.filter(
                    CloudDisk.id == parent_id,
                    CloudDisk.user_id == toc_user.id,
                    CloudDisk.item_type == 'folder',
                    CloudDisk.is_deleted == False
                ).first()

                if not parent_folder:
                    raise CloudDiskFolderNotFoundError()
            
            # 检查同一父目录下是否已存在同名文件夹
            existing_folder = CloudDisk.query.filter(
                CloudDisk.user_id == toc_user.id,
                CloudDisk.parent_id == parent_id,
                CloudDisk.name == name,
                CloudDisk.item_type == 'folder',
                CloudDisk.is_deleted == False
            ).first()
            
            if existing_folder:
                return {
                    'result': 'fail',
                    'message': '同一目录下已存在同名文件夹'
                }, 400
            
            # 创建文件夹
            folder_id = self.service.create_folder(toc_user, name, parent_id)
            
            return {
                'data': {
                    'id': str(folder_id)
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"创建文件夹失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class FirstLevelFoldersAPI(Resource):
    """一级文件夹列表 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def get(self, toc_user: ToCUser) -> Dict[str, Any]:
        """获取一级文件夹列表
        
        Returns:
            Dict[str, Any]: 一级文件夹列表
        """
        try:
            # 获取一级文件夹列表
            folders = self.service.get_first_level_folders(current_user)
            
            return {
                'data': folders,
                'result': 'success'
            }, 200
            
        except CloudDiskError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"获取一级文件夹列表失败: {str(e)}")
            return {
                'result': 'fail',
                'message': "获取一级文件夹列表失败"
            }, 500


class MoveItemAPI(Resource):
    """移动项目 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """将文件或文件夹移动到其他文件夹下
        
        JSON参数:
            target_id: 目标文件夹ID（可选，如果为空则移动到根目录）
            move_item_id: 要移动的项目ID
        
        Returns:
            Dict[str, Any]: 是否移动成功
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            move_item_id_str = data.get('move_item_id')
            if not move_item_id_str:
                raise CloudDiskInvalidParameterError("移动项目ID不能为空")
            
            try:
                move_item_id = uuid.UUID(move_item_id_str)
            except ValueError:
                raise CloudDiskInvalidParameterError("无效的移动项目ID")
            
            target_id_str = data.get('target_id')
            target_id = None
            if target_id_str:
                try:
                    target_id = uuid.UUID(target_id_str)
                except ValueError:
                    raise CloudDiskInvalidParameterError("无效的目标文件夹ID")

            # 移动项目
            success = self.service.move_item(current_user, move_item_id, target_id)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': "项目或目标文件夹不存在"
                }, 404
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except CloudDiskError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"移动项目失败: {str(e)}")
            return {
                'result': 'fail',
                'message': "移动项目失败"
            }, 500


class SharedStatusAPI(Resource):
    """共享状态 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, item_id: str, toc_user: ToCUser) -> Dict[str, Any]:
        """更新项目的共享状态
        
        URL参数:
            item_id: 项目ID
        
        JSON参数:
            shared: 是否共享
        
        Returns:
            Dict[str, Any]: 是否更新成功
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            shared = data.get('shared')
            if shared is None:
                return {
                    'error': 'invalid_parameter',
                    'description': '共享状态不能为空'
                }, 400
            
            # 更新共享状态
            success = self.service.update_shared_status(toc_user, item_id, shared)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': "项目不存在"
                }, 404
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"更新共享状态失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class CloudDiskFileUploadAPI(Resource):
    """云盘文件上传 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """将文件绑定到文件夹中
        
        JSON参数:
            upload_file_id: 上传文件ID
            folder_id: 文件夹ID（可选，如果为空则添加到根目录）
        
        Returns:
            Dict[str, Any]: 新创建的云盘项目ID
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            upload_file_id_str = data.get('upload_file_id')
            if not upload_file_id_str:
                return {
                    'error': 'invalid_parameter',
                    'description': '上传文件ID不能为空'
                }, 400
            
            try:
                upload_file_id = uuid.UUID(upload_file_id_str)
            except ValueError:
                return {
                    'error': 'invalid_parameter',
                    'description': '无效的上传文件ID'
                }, 400
            
            folder_id_str = data.get('folder_id')
            folder_id = None
            if folder_id_str:
                try:
                    folder_id = uuid.UUID(folder_id_str)
                except ValueError:
                    return {
                        'error': 'invalid_parameter',
                        'description': '无效的文件夹ID'
                    }, 400
            
            # 绑定文件
            item_id = self.service.bind_file_to_folder(toc_user, upload_file_id, folder_id)
            
            return {
                'data': {
                    'id': str(item_id)
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"绑定文件失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class CloudDiskRemoveAPI(Resource):
    """云盘项目删除 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def delete(self, item_id: str, toc_user: ToCUser) -> Dict[str, Any]:
        """删除云盘项目（文件或文件夹）
        
        URL参数:
            item_id: 项目ID
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 删除项目
            success = self.service.remove_item(current_user, item_id)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': "项目不存在"
                }, 404
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except ValueError:
            return {
                'result': 'fail',
                'message': "无效的项目ID"
            }, 400
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"删除项目失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class UpdateItemNameAPI(Resource):
    """更新项目名称 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, item_id: str, toc_user: ToCUser) -> Dict[str, Any]:
        """更新项目名称
        
        URL参数:
            item_id: 项目ID
            
        JSON参数:
            name: 新名称
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            new_name = data.get('name')
            if not new_name:
                return {
                    'error': 'invalid_parameter',
                    'description': '名称不能为空'
                }, 400

            # 更新名称
            success, error_msg = self.service.update_item_name(current_user, item_id, new_name)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': error_msg
                }, 400
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"更新项目名称失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class BatchMoveItemsAPI(Resource):
    """批量移动项目 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """批量移动项目到指定文件夹
        
        JSON参数:
            target_id: 目标文件夹ID（可选，如果为空则移动到根目录）
            move_item_ids: 要移动的项目ID列表
        
        Returns:
            Dict[str, Any]: 移动结果
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            move_item_ids = data.get('move_item_ids', [])
            if not move_item_ids:
                return {
                    'error': 'invalid_parameter',
                    'description': '移动项目ID列表不能为空'
                }, 400
            
            # 转换项目ID列表为UUID类型
            try:
                move_item_ids = [uuid.UUID(item_id) for item_id in move_item_ids]
            except ValueError:
                return {
                    'error': 'invalid_parameter',
                    'description': '无效的项目ID格式'
                }, 400
            
            target_id_str = data.get('target_id')
            target_id = None
            if target_id_str:
                try:
                    target_id = uuid.UUID(target_id_str)
                except ValueError:
                    return {
                        'error': 'invalid_parameter',
                        'description': '无效的目标文件夹ID'
                    }, 400

            # 批量移动项目
            success, error_msg = self.service.batch_move_items(toc_user, move_item_ids, target_id)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': error_msg
                }, 400
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"批量移动项目失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class BatchRemoveItemsAPI(Resource):
    """批量删除项目 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """批量删除项目
        
        JSON参数:
            item_ids: 要删除的项目ID列表
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            item_ids = data.get('item_ids', [])
            if not item_ids:
                return {
                    'error': 'invalid_parameter',
                    'description': '删除项目ID列表不能为空'
                }, 400
            
            # 批量删除项目
            success, error_msg = self.service.batch_remove_items(toc_user, item_ids)
            
            if not success:
                return {
                    'result': 'fail',
                    'message': error_msg
                }, 400
            
            return {
                'data': {
                    'success': True
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"批量删除项目失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class SearchItemsAPI(Resource):
    """搜索云盘项目 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def get(self, toc_user: ToCUser) -> Dict[str, Any]:
        """搜索云盘项目
        
        URL参数:
            keyword: 搜索关键词（可选）
            type: 搜索类型，personal（个人）或shared（共享），默认为personal
            page: 页码，默认为1
            limit: 每页数量，默认为20
        
        Returns:
            Dict[str, Any]: {
                'data': [...],  # 项目列表
                'total': int,   # 总记录数
                'page': int,    # 当前页码
                'limit': int,   # 每页数量
                'pages': int    # 总页数
            }
        """
        try:
            # 获取请求参数
            keyword = request.args.get('keyword', '').strip()
            
            search_type = request.args.get('type', 'personal')
            if search_type not in ['personal', 'shared']:
                return {
                    'error': 'invalid_parameter',
                    'description': '无效的搜索类型'
                }, 400
            
            # 获取分页参数
            try:
                page = max(1, int(request.args.get('page', 1)))
                limit = max(1, min(50, int(request.args.get('limit', 20))))  # 限制最大为50
            except ValueError:
                return {
                    'error': 'invalid_parameter',
                    'description': '无效的分页参数'
                }, 400
            
            # 搜索项目
            result = self.service.search_items(
                account=current_user,
                keyword=keyword,
                search_type=search_type,
                page=page,
                limit=limit
            )
            
            return {
                'data': result,
                'result': 'success'
            }, 200
            
        except Exception as e:
            current_app.logger.error(f"搜索云盘项目失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class SearchFoldersAPI(Resource):
    """搜索文件夹 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def get(self, toc_user: ToCUser) -> Dict[str, Any]:
        """搜索文件夹并返回带路径的结果
        
        URL参数:
            keyword: 搜索关键词（可选）
        
        Returns:
            Dict[str, Any]: {
                'data': [  # 文件夹列表
                    {
                        'id': str,          # 文件夹ID
                        'name': str,        # 文件夹名称
                        'path': str,        # 完整路径
                        'created_at': str,  # 创建时间
                        'updated_at': str   # 更新时间
                    },
                    ...
                ]
            }
        """
        try:
            # 获取请求参数
            keyword = request.args.get('keyword', '').strip()

            # 搜索文件夹
            result = self.service.search_folders(current_user, keyword)
            
            return {
                'data': result,
                'result': 'success'
            }, 200
            
        except Exception as e:
            current_app.logger.error(f"搜索文件夹失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class BatchCloudDiskFileUploadAPI(Resource):
    """批量云盘文件上传 API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    @toc_login_required
    def post(self, toc_user: ToCUser) -> Dict[str, Any]:
        """批量将文件绑定到文件夹中
        
        JSON参数:
            upload_file_ids: 上传文件ID列表
            folder_id: 文件夹ID（可选，如果为空则添加到根目录）
        
        Returns:
            Dict[str, Any]: {
                'success': bool,          # 是否全部成功
                'message': str,           # 错误信息
                'data': [{               # 成功绑定的文件列表
                    'id': str,           # 云盘项目ID
                    'name': str,         # 文件名
                    'upload_file_id': str # 上传文件ID
                }, ...]
            }
        """
        try:
            # 获取请求参数
            data = request.get_json() if request.is_json else {}
            
            upload_file_ids = data.get('upload_file_ids', [])
            if not upload_file_ids:
                return {
                    'error': 'invalid_parameter',
                    'description': '上传文件ID列表不能为空'
                }, 400
            
            # 转换文件ID列表为UUID类型
            try:
                upload_file_ids = [uuid.UUID(file_id) for file_id in upload_file_ids]
            except ValueError:
                return {
                    'error': 'invalid_parameter',
                    'description': '无效的文件ID格式'
                }, 400
            
            folder_id_str = data.get('folder_id')
            folder_id = None
            if folder_id_str:
                try:
                    folder_id = uuid.UUID(folder_id_str)
                except ValueError:
                    return {
                        'error': 'invalid_parameter',
                        'description': '无效的文件夹ID'
                    }, 400
            
            # 批量绑定文件
            success, message, result = self.service.batch_bind_files_to_folder(
                current_user,
                upload_file_ids,
                folder_id
            )
            
            if not success and not result:
                return {
                    'result': 'fail',
                    'message': message
                }, 400
            
            return {
                'data': result,
                'result': 'success',
                'message': message if not success else ''
            }, 200 if success else 207  # 使用207表示部分成功
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"批量绑定文件失败: {str(e)}")
            return {
                'result': 'fail',
                'message': str(e)
            }, 500


class GetFileUrlAPI(Resource):
    """获取文件URL API"""
    
    def __init__(self):
        self.service = CloudDiskService()

    def get(self) -> Dict[str, Any]:
        """根据文件ID获取文件的签名URL
        
        URL参数:
            file_id: 文件ID
        
        Returns:
            Dict[str, Any]: {
                'data': {
                    'url': str  # 文件的签名URL
                }
            }
        """
        try:

            parser = reqparse.RequestParser()
            parser.add_argument("file_id", type=str, required=True, location="args")
            args = parser.parse_args()

            file_id = args["file_id"]
            if (not file_id):
                return {
                    'data': {},
                    'result': 'false'
                }, 200
            tool_file: ToolFile | None = (
                db.session.query(ToolFile)
                .filter(
                    ToolFile.id == file_id,
                )
                .first()
            )

            if tool_file:
                # 获取文件的签名URL
                extension = tool_file.name.split(".")[-1]
                file_url = ToolFileManager.sign_file(tool_file.id, f".{extension}")
            else:
                file_url = file_helpers.get_signed_file_url(file_id)

            
            return {
                'data': {
                    'url': file_url
                },
                'result': 'success'
            }, 200
            
        except CloudDiskOperationError as e:
            return {
                'result': 'fail',
                'message': e.description
            }, e.code
        except Exception as e:
            current_app.logger.error(f"获取文件URL失败: {str(e)}")
            return {
                'result': 'fail',
                'message': "获取文件URL失败"
            }, 500


# 注册路由
api.add_resource(CloudDiskListAPI, '/cloud-disk/list')  # GET - 获取云盘文件列表
api.add_resource(CloudDiskFolderAPI, '/cloud-disk/folder/create')  # POST - 创建文件夹
api.add_resource(FirstLevelFoldersAPI, '/cloud-disk/folders/first-level')  # GET - 获取一级文件夹列表
api.add_resource(MoveItemAPI, '/cloud-disk/item/move')  # POST - 移动项目
api.add_resource(BatchMoveItemsAPI, '/cloud-disk/items/batch-move')  # POST - 批量移动项目
api.add_resource(SharedStatusAPI, '/cloud-disk/item/<uuid:item_id>/shared')  # POST - 更新共享状态
api.add_resource(CloudDiskFileUploadAPI, '/cloud-disk/file/upload')  # POST - 绑定文件到文件夹
api.add_resource(CloudDiskRemoveAPI, '/cloud-disk/item/<uuid:item_id>')  # DELETE - 删除项目
api.add_resource(BatchRemoveItemsAPI, '/cloud-disk/items/batch-remove')  # POST - 批量删除项目
api.add_resource(UpdateItemNameAPI, '/cloud-disk/item/<uuid:item_id>/rename')  # POST - 更新项目名称
api.add_resource(SearchItemsAPI, '/cloud-disk/items/search')  # GET - 搜索云盘项目
api.add_resource(SearchFoldersAPI, '/cloud-disk/folders/search')  # GET - 搜索文件夹
api.add_resource(BatchCloudDiskFileUploadAPI, '/cloud-disk/files/batch-upload')  # POST - 批量绑定文件到文件夹
api.add_resource(GetFileUrlAPI, '/cloud-disk/files/file-preview')  # GET - 获取文件URL