# -*- coding: utf-8 -*-
"""
DAG汇总API模块
提供计划执行汇总的流式获取接口
"""

import json
import logging
import time
from flask import Response
from flask_restful import Resource, reqparse

from controllers.web import api
from extensions.ext_redis import redis_client
from libs.helper import compact_generate_response
from models.dag_flow import DagFlowRuns
from services.dag.summary.plan_result_summary import PlanResultSummaryService
from libs.toc_login import toc_login_required
from models import EndUser, db

logger = logging.getLogger(__name__)

class PlanSummaryApi(Resource):
    """计划汇总流式获取API"""
    
    #@toc_login_required
    def post(self):
        """获取计划执行汇总，以SSE流式方式返回
        
        通过从Redis中读取由plan_result_summary服务生成的汇总结果，
        以Server-Sent Events (SSE) 方式流式返回给前端。
        
        Request Body:
            {
                "inputs": {
                    "run_id": "DAG运行ID"
                },
                "response_mode": "streaming"  // 必须为streaming才会返回流式结果
            }
            
        Returns:
            Response: 流式SSE响应
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('inputs', type=dict, required=True, location='json')
            parser.add_argument('response_mode', type=str, required=False, location='json')
            parser.add_argument('conversation_id', type=str, required=False, location='json')
            args = parser.parse_args()
            
            # 从inputs中获取必要参数
            inputs = args.get('inputs', {})
            run_id = inputs.get('run_id')
            conversation_id = args.get('conversation_id')
            
            # 验证必要参数
            if not run_id:
                return {
                    "error": "run_id is required in inputs",
                    "status": "error"
                }, 400
            
            logger.info(f"开始获取计划汇总， run_id: {run_id}")
            
            # 验证response_mode
            response_mode = args.get('response_mode')
            if response_mode != 'streaming':
                return {
                    "error": "response_mode must be 'streaming'",
                    "status": "error"
                }, 400
            
            # 检查汇总是否存在，如不存在则触发生成
            redis_key = f"plan_summary:{run_id}"
            is_existing = redis_client.exists(redis_key) or redis_client.exists(f"{redis_key}:completed") or redis_client.exists(f"{redis_key}:error")
            
            if not is_existing:
                logger.info(f"未找到汇总数据，触发生成： run_id: {run_id}")

            
            # 使用custom_sse_response包装流式响应而不是compact_generate_response
            return Response(
                self._stream_summary_from_redis_as_sse(run_id, conversation_id),
                content_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"
                }
            )
            
        except Exception as e:
            logger.error(f"获取计划汇总失败: {str(e)}")
            return {
                "error": str(e),
                "status": "error"
            }, 500
    
    def _stream_summary_from_redis_as_sse(self, run_id, conversation_id):
        """从Redis读取汇总数据并流式返回为SSE格式
        
        Args:
            run_id: DAG运行ID
            
        Yields:
            bytes: 符合SSE格式的字节流数据
        """
        # Redis键名定义
        redis_key = f"plan_summary:{run_id}"         # 主键，存储流式内容
        completed_key = f"{redis_key}:completed"     # 完成状态键
        error_key = f"{redis_key}:error"             # 错误状态键
        
        # 内容追踪和时间控制
        last_content = ""                            # 上次返回的内容
        max_wait_time = 300                          # 最大等待时间(秒)
        start_time = time.time()                     # 开始时间
        poll_count = 0                               # 轮询计数器
        summary_completed = False                    # 汇总是否已完成
        
        try:
            # 发送一个"等待"消息，建立连接
            logger.info(f"发送等待消息，建立SSE连接: {run_id}")
            data = {
                "event": "waiting",
                "run_id": run_id,
                "timestamp": int(time.time()),
                "conversation_id": conversation_id
            }
            yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
            
            # 保持轮询直到获得结果或超时
            while True:
                current_time = time.time()
                poll_count += 1
                
                # 超时检查
                if current_time - start_time > max_wait_time:
                    logger.warning(f"获取汇总超时: {run_id}")
                    data = {
                        "event": "error",
                        "error": "Summary retrieval timeout"
                    }
                    yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
                    break
                
                # 检查错误状态
                error_data = redis_client.get(error_key)
                if error_data:
                    try:
                        error_json = json.loads(error_data)
                        logger.warning(f"汇总生成出错: {run_id}, {error_json.get('error', '未知错误')}")
                        
                        # 提取ID信息
                        message_id = error_json.get("id")
                        task_id = error_json.get("task_id")
                        created_at = error_json.get("created_at")
                        
                        error_response = {
                            "event": "error",
                            "error": error_json.get("error", "Unknown error")
                        }
                        
                        # 添加ID信息，如果存在
                        if message_id:
                            error_response["message_id"] = message_id
                        if task_id:
                            error_response["task_id"] = task_id
                        if created_at:
                            error_response["created_at"] = created_at
                        
                        yield f"data: {json.dumps(error_response)}\n\n".encode('utf-8')
                        summary_completed = True  # 标记为已完成
                    except Exception as e:
                        logger.error(f"解析错误数据失败: {str(e)}")
                        data = {
                            "event": "error",
                            "error": "Error parsing error data"
                        }
                        yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
                    break
                
                # 检查完成状态
                completed_data = redis_client.get(completed_key)
                if completed_data:
                    try:
                        completed_json = json.loads(completed_data)
                        logger.info(f"汇总生成完成: {run_id}")
                        
                        # 提取ID信息
                        message_id = completed_json.get("id")
                        task_id = completed_json.get("task_id")
                        created_at = completed_json.get("created_at")
                        conversation_id = completed_json.get("conversation_id")
                        
                        # 获取完整内容
                        full_summary = completed_json.get("answer", "")
                        
                        # 只发送增量内容
                        if full_summary and full_summary != last_content:
                            new_content = full_summary[len(last_content):]
                            if new_content:
                                message_response = {
                                    "event": "message",
                                    "answer": new_content,
                                    "mode": "streaming"
                                }
                                
                                # 添加ID信息，如果存在
                                if message_id:
                                    message_response["message_id"] = message_id
                                if task_id:
                                    message_response["task_id"] = task_id
                                if created_at:
                                    message_response["created_at"] = created_at
                                if conversation_id:
                                    message_response["conversation_id"] = conversation_id
                                    
                                yield f"data: {json.dumps(message_response)}\n\n".encode('utf-8')
                            
                            # 更新已发送内容
                            last_content = full_summary
                        
                        # 发送结束消息
                        end_response = {"event": "message_end"}
                        
                        # 添加ID信息，如果存在
                        if message_id:
                            end_response["message_id"] = message_id
                        if task_id:
                            end_response["task_id"] = task_id
                        if created_at:
                            end_response["created_at"] = created_at
                        if conversation_id:
                            end_response["conversation_id"] = conversation_id

                            
                        yield f"data: {json.dumps(end_response)}\n\n".encode('utf-8')
                        summary_completed = True  # 标记为已完成
                    except Exception as e:
                        logger.error(f"解析完成数据失败: {str(e)}")
                        data = {
                            "event": "error",
                            "error": "Error parsing completed summary data"
                        }
                        yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
                    break
                
                # 获取主键中的最新内容
                current_data = redis_client.get(redis_key)
                if current_data:
                    try:
                        current_json = json.loads(current_data)
                        
                        # 提取ID信息
                        message_id = current_json.get("id")
                        task_id = current_json.get("task_id")
                        created_at = current_json.get("created_at")
                        conversation_id = current_json.get("conversation_id")

                        # 获取当前内容
                        current_content = current_json.get("answer", "")
                        
                        # 只发送增量内容
                        if current_content and current_content != last_content:
                            new_content = current_content[len(last_content):]
                            if new_content:
                                message_response = {
                                    "event": "message",
                                    "answer": new_content,
                                    "mode": "streaming"
                                }
                                
                                # 添加ID信息，如果存在
                                if message_id:
                                    message_response["message_id"] = message_id
                                if task_id:
                                    message_response["task_id"] = task_id
                                if created_at:
                                    message_response["created_at"] = created_at
                                if conversation_id:
                                    message_response["conversation_id"] = conversation_id
                                    
                                yield f"data: {json.dumps(message_response)}\n\n".encode('utf-8')
                            
                            # 更新已发送内容
                            last_content = current_content
                    except Exception as e:
                        logger.error(f"解析流式数据失败: {str(e)}")
                
                # 发送保活消息以维持连接
                if poll_count % 50 == 0:  # 每约10秒发送一次保活消息
                    logger.info(f"发送保活消息: {run_id}")
                    data = {
                        "event": "waiting",
                        "run_id": run_id,
                        "timestamp": int(current_time),
                        "conversation_id": conversation_id
                    }
                    yield f"data: {json.dumps(data)}\n\n".encode('utf-8')
                
                # 控制轮询频率 - 动态调整休眠时间
                elapsed_time = current_time - start_time
                if elapsed_time < 10:
                    # 开始的10秒内快速轮询
                    time.sleep(0.1)
                elif elapsed_time < 60:
                    # 10秒到1分钟之间适中轮询
                    time.sleep(0.2)
                else:
                    # 1分钟后降低轮询频率
                    time.sleep(0.5)
        finally:
            # 在流结束后（无论是正常完成还是异常），清理Redis数据
            if summary_completed:
                try:
                    # 如果汇总已完成，清理所有相关的Redis键
                    logger.info(f"清理Redis汇总数据: {run_id}")
                    redis_client.delete(redis_key)
                    redis_client.delete(completed_key)
                    redis_client.delete(error_key)
                    # 还可以清理锁（如果有的话）
                    lock_key = f"plan_summary_lock:{run_id}"
                    redis_client.delete(lock_key)
                except Exception as e:
                    logger.error(f"清理Redis数据时出错: {str(e)}")

# 注册API路由
api.add_resource(PlanSummaryApi, '/dag/summary')