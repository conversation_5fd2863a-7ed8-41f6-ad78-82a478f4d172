import logging
import traceback

import flask_login  # type: ignore
from flask import current_app
from flask_restful import Resource, marshal, reqparse  # type: ignore

from controllers.web import api
from controllers.web.error import InvalidInvitationCodeError
from services.invitation_service import InvitationApplicationService


class ValidateInvitationCodeApi(Resource):

    def post(self):
        # 邀请码是否存在
        # 邀请码是否可用
        parser = reqparse.RequestParser()
        parser.add_argument("invitation_code", type=str, required=True, location="json")
        args = parser.parse_args()
        print(f"Validate invitation code: {args['invitation_code']}")

        try:
            # 验证邀请码有效性
            invitation_service = InvitationApplicationService()
            invitation_service.validate_invitation_code(args['invitation_code'])
            return {
                "result": "success",
                "data": {
                    "valid": True
                }
            }
        except InvalidInvitationCodeError as e:
            current_app.logger.error(f"ValidateInvitationCodeApi(校验邀请码是否可用): {traceback.format_exc()}")
            return {"result": "fail", "code": "invalid_code", "message": str(e)}, 400


class ApplyWithInvitationCodeApi(Resource):
    # @setup_required
    def post(self):
        # 校验邀请码是否可用
        # 登记邀请码申请
        parser = reqparse.RequestParser()
        parser.add_argument("phone", type=str, required=True, location="json")
        parser.add_argument("apply_reason", type=str, required=False, location="json")
        # parser.add_argument("invitation_code", type=str, required=True, location="json")
        args = parser.parse_args()
        print(f"Apply with phone: {args['phone']}")

        try:
            # 创建申请记录
            invitation_service = InvitationApplicationService()
            application = invitation_service.apply_invitation_code(
                phone=args['phone'],
                apply_reason=args["apply_reason"]
            )

            return {
                "result": "success",
                "data": {
                    "application_id": str(application.id),
                    "applied_at": application.created_at.isoformat()
                }
            }
        except Exception as e:
            current_app.logger.error(f"ApplyWithInvitationCodeApi(登记邀请码申请): {traceback.format_exc()}")
            return {"result": "fail", "message": str(e)}, 400

# 校验邀请码是否可用
api.add_resource(ValidateInvitationCodeApi, "/invitationcodes/validate")
# 登记邀请码申请
api.add_resource(ApplyWithInvitationCodeApi, "/invitationcodes/apply")