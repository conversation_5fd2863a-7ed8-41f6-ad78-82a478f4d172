import logging

from flask import jsonify, request
from flask_restful import Resource
from litellm.proxy.management_endpoints.customer_endpoints import end_user_info
from werkzeug.exceptions import NotFound, Unauthorized

from controllers.web import api
from controllers.console.error import NoFileUploadedError
from core.file import helpers as file_helpers
from libs.passport import PassportService
from services.account_service import AccountService


from models import UploadFile, Account, EndUser
from models.toc_user import ToCUser
from extensions.ext_database import db
from services.file_service import FileService
from configs.remote_settings_sources.apollo import apollo_client

logger = logging.getLogger(__name__)


class WPSFileInfoApi(Resource):
    def get(self, file_id):
        """获取文件基本信息

        Returns:
            {
                "code": 0,
                "data": {
                    "id": "文件ID",
                    "name": "文件名称",
                    "size": "文件大小",
                    "create_time": "创建时间戳",
                    "creator_id": "创建者ID",
                    "modifier_id": "修改者ID",
                    "modify_time": "修改时间戳",
                    "version": "版本号"
                }
            }
        """
        # get_user_id_from_auth_header()
        try:
            # 获取文件信息，参考现有的文件查询逻辑
            upload_file = db.session.query(UploadFile).filter(
                UploadFile.id == file_id
            ).first()

            if not upload_file:
                raise NotFound("File not found")

            # 转换为 WPS 所需的响应格式
            return jsonify({
                "code": 0,
                "data": {
                    "id": str(upload_file.id),
                    "name": upload_file.name,
                    "size": upload_file.size,
                    "create_time": int(upload_file.created_at.timestamp()),
                    "creator_id": format_uuid_to_wps_id(str(upload_file.created_by)),
                    "modifier_id": format_uuid_to_wps_id(str(upload_file.created_by)),  # 如果没有修改者信息，使用创建者
                    "modify_time": int(upload_file.created_at.timestamp()),  # 如果没有修改时间，使用创建时间
                    "version": upload_file.version,  # 默认版本号
                }
            })

        except Exception as e:
            logger.error(f"Get WPS file info error: {str(e)}")
            return {
                "code": 500,
                "message": str(e)
            }


class BatchUsersApi(Resource):
    """批量获取用户信息的API"""

    def get(self):
        """获取多个用户信息

        URL参数:
            user_ids: 用户ID列表，可以传递多个，如 /v3/3rd/users?user_ids=1&user_ids=2

        Returns:
            {
                "code": 0,
                "data": [{
                    "id": "用户ID",
                    "name": "用户名称"
                }]
            }
        """
        # 获取URL参数中的user_ids列表
        wps_user_ids = request.args.getlist('user_ids')

        if not wps_user_ids:
            return {
                'code': 400,
                'message': '用户ID列表不能为空'
            }

        logger.info(f"wps预览获取多个用户信息, user_ids: {wps_user_ids}")

        try:
            # 转换用户ID格式
            user_ids = [format_wps_id_to_uuid(wps_id) for wps_id in wps_user_ids]

            # 查询用户信息
            accounts = db.session.query(ToCUser).filter(
                ToCUser.end_user_id.in_(user_ids)
            ).all()

            # 格式化返回数据
            account_list = []
            for account in accounts:
                account_list.append({
                    'id': format_uuid_to_wps_id(account.end_user_id),  # 返回时转换回WPS格式
                    'name': account.name,
                    'avatar_url': ''
                })

            return jsonify({
                'code': 0,
                'data': account_list
            })

        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return {
                'code': 500,
                'message': f'获取用户信息失败: {str(e)}'
            }


class WPSFileDownloadUrlApi(Resource):
    def get(self, file_id):
        """获取文件下载地址

        Returns:
            {
                "code": 0,
                "data": {
                    "download_url": "文件下载地址",
                    "expires_in": 3600  # 下载链接有效期(秒)
                }
            }
        """
        # get_user_id_from_auth_header()
        try:
            # 获取文件信息
            upload_file = db.session.query(UploadFile).filter(
                UploadFile.id == file_id
            ).first()

            if not upload_file:
                raise NotFound("File not found")

            # 生成下载链接
            download_url = file_helpers.get_signed_file_url(upload_file.id)

            return jsonify({
                "code": 0,
                "data": {
                    "url": download_url,
                    "expires_in": 3600
                }
            })

        except Exception as e:
            logger.error(f"Get file download url error: {str(e)}")
            return {
                "code": 500,
                "message": str(e)
            }


def format_uuid_to_wps_id(uuid_str: str) -> str:
    """将 UUID 格式转换为 WPS 要求的用户 ID 格式

    Args:
        uuid_str: UUID 字符串

    Returns:
        str: 符合 WPS 要求的用户 ID（只包含字母、数字和下划线，且不以下划线开头）
    """
    # 将所有破折号替换为下划线
    clean_id = uuid_str.replace('-', '_')
    # 确保以字母开头
    if clean_id[0].isdigit() or clean_id[0] == '_':
        clean_id = f"u{clean_id}"
    return clean_id


class WPSFilePermissionApi(Resource):
    def get(self, file_id):
        """获取文件用户权限

        Returns:
            {
                "code": 0,
                "data": {
                    "user_id": "用户ID",
                    "read": 1,      # 预览权限
                    "update": 1,    # 编辑权限
                    "download": 1,  # 下载权限
                    "rename": 1,    # 重命名权限
                    "history": 1,   # 历史记录权限
                    "copy": 1,      # 拷贝权限
                    "print": 1,     # 打印权限
                    "saveas": 1,    # 另存权限
                    "comment": 1    # 评论权限
                }
            }
        """
        #user_id = get_user_id_from_auth_header()
        #if not user_id:
            #raise Unauthorized("Invalid Authorization token.")
        try:
            # 获取文件信息
            upload_file = db.session.query(UploadFile).filter(
                UploadFile.id == file_id
            ).first()

            if not upload_file:
                raise NotFound("File not found")

            # 默认给予所有权限
            return jsonify({
                "code": 0,
                "data": {
                    "user_id": format_uuid_to_wps_id(str(upload_file.created_by)),  # 使用文件创建者ID
                    "read": 1,
                    "update": 1,
                    "download": 1,
                    "rename": 0,
                    "history": 0,
                    "copy": 1,
                    "print": 1,
                    "saveas": 1,
                    "comment": 1
                }
            })

        except Exception as e:
            logger.error(f"Get WPS file permission error: {str(e)}")
            return {
                "code": 500,
                "message": str(e)
            }


class WPSFileEditPrepare(Resource):
    def get(self, file_id):
        """获取文件编辑准备信息"""
        return jsonify({
            "code": 0,
            "data": {
                "digest_types": ["sha256"]
            },
            "message": ""
        })


class WPSFileUpload(Resource):
    def post(self, file_id: str, yw_token: str):
        """上传文件"""
        logger.info(f"Start processing WPS file upload for file_id: {file_id}")
        try:
            file = request.files["file"]
            # check file
            if "file" not in request.files:
                logger.error(f"No file uploaded for file_id: {file_id}")
                raise NoFileUploadedError()
            if not yw_token:
                logger.error(f"Missing X-Weboffice-Token for file_id: {file_id}")
                raise Unauthorized("X-Weboffice-Token is missing")

            if not yw_token.startswith("Bearer "):
                yw_token = "Bearer " + yw_token
                logger.info(f"Added 'Bearer ' prefix to yw_token for file_id: {file_id}")

            '''
            wps  X-Weboffice-Token  获取登录用户信息
            '''
            account = get_account_by_auth_token(yw_token)
            if not account:
                logger.error(f"Account not found for file_id: {file_id}")
                raise Unauthorized("account is not exit")

            logger.info(f"Updating file {file_id} with filename: {file.filename}")
            FileService.update_file( 
                file_id=file_id,
                filename=file.filename,
                content=file.read(),
                mimetype=file.mimetype,
                user=account)
            
            logger.info(f"Successfully uploaded file {file_id}")
            return jsonify({
                "code": 0,
                "data": {
                    "file_id": file_id
                },
                "message": ""
            })
        except Exception as e:
            logger.error(f"Error uploading file {file_id}: {str(e)}")
            raise e




def get_user_id_from_auth_header() -> str:
    auth_header = request.headers.get("X-Weboffice-Token", "")
    auth_scheme, auth_token = auth_header.split(None, 1)
    decoded = PassportService().verify(auth_token)
    user_id = decoded.get("user_id")
    if not user_id:
        raise Unauthorized("Invalid Authorization token.")
    return user_id


def format_wps_id_to_uuid(wps_id: str) -> str:
    """将 WPS 格式的用户 ID 转换为 UUID 格式

    Args:
        wps_id: WPS格式的用户ID（只包含字母、数字和下划线）

    Returns:
        str: UUID格式的用户ID（包含破折号）
    """
    # 如果以'u'开头，去掉'u'
    if wps_id.startswith('u'):
        wps_id = wps_id[1:]
    # 将下划线替换为破折号
    return wps_id.replace('_', '-')




"""
wps文档在线编辑回调
"""
class WPSFileUploadPrepareApi(Resource):
    def get(self,file_id):
        logger.info(f"Start processing WPS file WPSFileUploadPrepareApi for file_id: {file_id}")
        return  jsonify({
                "code": 0,
                "data":{
                
                },
                 "message": ""
            })

class WPSFileUploadAddressApi(Resource):
    def post(self,file_id):
        try:
            logger.info(f"Start processing WPS file WPSFileUploadAddressApi for file_id: {file_id}")
            # 从环境配置获取上传完成URL
            logger.info(f"WPSFileUploadAddressApi  Request headers: {dict(request.headers)},file_id: {file_id}")
            upload_complete_url = apollo_client.get_value(key="WPS_FILE_UPLOAD_COMPLETE_URL")
            authorization = request.headers.get("X-Weboffice-Token", "")
            # 替换URL中的文件ID占位符
            upload_complete_url = upload_complete_url.replace('{file_id}', str(file_id))
            upload_complete_url=upload_complete_url.replace('{yw_token}', str(authorization))
            return jsonify({
                "code": 0,
                "data": {
                    "method":"POST",
                    "url": upload_complete_url
                },
                "message": ""
            })
        except Exception as e:
            logger.error(f"Get WPS file upload address error: {str(e)}")
            return {
                "code": 500,
                "message": str(e)
            }
    



class WPSFileUploadCompleteApi(Resource):
    def post(self,file_id):
        try:
            logger.info(f"Start processing WPS file WPSFileUploadCompleteApi for file_id: {file_id}")
            # 获取文件信息
            upload_file = db.session.query(UploadFile).filter(
                UploadFile.id == file_id
            ).first()

            if not upload_file:
                raise NotFound("File not found")
            
            return jsonify({
                "code": 0,
                "data": {
                "create_time": int(upload_file.created_at.timestamp()),
                "creator_id":format_uuid_to_wps_id(str(upload_file.created_by)) ,
                "id": upload_file.id,
                "modifier_id": format_uuid_to_wps_id(str(upload_file.created_by)),
                "modify_time": int(upload_file.created_at.timestamp()),
                "name": upload_file.name,
                "size": upload_file.size,
                "version": upload_file.version
                }
            })
        except Exception as e:
            logger.error(f"Upload file error: {str(e)}")
            return {
                "code": 500,
                "message": str(e)
            }



def get_account_by_auth_token(auth_token: str):
    """
    Get account by authorization token.
    
    :param auth_token: The authorization token from Authorization header
    :return: Account object if token is valid, None otherwise
    :raises Unauthorized: If the authorization token is invalid or missing
    """
    if not auth_token:
        raise Unauthorized("Authorization is missing")
        
    if " " not in auth_token:
        raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <token>' format.")
        
    auth_scheme, auth_token = auth_token.split(None, 1)
    auth_scheme = auth_scheme.lower()
    if auth_scheme.lower() != "bearer":
        raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <token>' format.")
        
    try:
        decoded = PassportService().verify(auth_token)
        user_id = decoded.get("user_id")
        end_user_id = decoded.get("end_user_id")
        
        if not user_id:
            raise Unauthorized("Invalid Authorization token.")

        account = AccountService.load_logged_in_account(account_id=user_id)
        if account:
            return account
        if end_user_id:
            end_user = db.session.query(EndUser).filter(EndUser.id == end_user_id).first()
            if end_user:
                return end_user
            else:
                raise Unauthorized("Account not found.")
        else:
            raise Unauthorized("Account not found.")
            
    except Exception as e:
        raise Unauthorized(str(e))



class FilePreview(Resource):
    def post(self,file_id):
           url= file_helpers.get_signed_file_url(file_id)
           return jsonify({
                "code": 0,
                "data": {
                    "url":url
                },

            })


api.add_resource(WPSFileInfoApi, "/v3/3rd/files/<string:file_id>")
api.add_resource(WPSFileDownloadUrlApi, "/v3/3rd/files/<string:file_id>/download")
api.add_resource(WPSFilePermissionApi, "/v3/3rd/files/<string:file_id>/permission")
api.add_resource(WPSFileUpload, "/v3/3rd/files/<string:file_id>/<string:yw_token>/upload")
api.add_resource(BatchUsersApi, "/v3/3rd/users")
api.add_resource(WPSFileUploadPrepareApi, "/v3/3rd/files/<string:file_id>/upload/prepare")
api.add_resource(WPSFileUploadAddressApi, "/v3/3rd/files/<string:file_id>/upload/address")
api.add_resource(WPSFileUploadCompleteApi, "/v3/3rd/files/<string:file_id>/upload/complete")
api.add_resource(FilePreview, "/v3/3rd/files/<string:file_id>/preview")
