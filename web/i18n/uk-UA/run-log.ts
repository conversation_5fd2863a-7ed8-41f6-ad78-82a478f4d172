const translation = {
  input: 'ВВЕДЕННЯ',
  result: 'РЕЗУЛЬТАТ',
  detail: 'ДЕТАЛІ',
  tracing: 'ВІДСЛІДКУВАННЯ',
  resultPanel: {
    status: 'СТАТУС',
    time: 'ЧАС ВИКОНАННЯ',
    tokens: 'ЗАГАЛЬНА КІЛЬКІСТЬ ТОКЕНІВ',
  },
  meta: {
    title: 'МЕТАДАНІ',
    status: 'Статус',
    version: 'Версія',
    executor: 'Виконавець',
    startTime: 'Час початку',
    time: 'Час виконання',
    tokens: 'Загальна кількість токенів',
    steps: 'Кроки виконання',
  },
  resultEmpty: {
    title: 'Цей запуск лише вихідного формату JSON,',
    tipLeft: 'будь ласка, перейдіть до ',
    link: 'панель деталей',
    tipRight: ' переглянути.',
  },
  circularInvocationTip: 'У поточному робочому процесі існує круговий виклик інструментів/вузлів.',
  actionLogs: 'Журнали дій',
}

export default translation
