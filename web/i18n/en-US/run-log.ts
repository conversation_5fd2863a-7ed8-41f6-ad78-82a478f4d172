const translation = {
  input: 'INPUT',
  result: 'RESULT',
  detail: 'DETAIL',
  tracing: 'TRACING',
  resultPanel: {
    status: 'STATUS',
    time: 'ELAPSED TIME',
    tokens: 'TOTAL TOKENS',
  },
  meta: {
    title: 'METADATA',
    status: 'Status',
    version: 'Version',
    executor: 'Executor',
    startTime: 'Start Time',
    time: 'Elapsed Time',
    tokens: 'Total Tokens',
    steps: 'Run Steps',
  },
  resultEmpty: {
    title: 'This run only output JSON format,',
    tipLeft: 'please go to the ',
    link: 'detail panel',
    tipRight: ' view it.',
  },
  actionLogs: 'Action Logs',
  circularInvocationTip: 'There is circular invocation of tools/nodes in the current workflow.',
}

export default translation
