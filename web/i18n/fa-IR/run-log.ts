const translation = {
  input: 'ورودی',
  result: 'نتیجه',
  detail: 'جزئیات',
  tracing: 'ردیاب<PERSON>',
  resultPanel: {
    status: 'وضعیت',
    time: 'زمان گذشته',
    tokens: 'کل توکن‌ها',
  },
  meta: {
    title: 'فراداده',
    status: 'وضعیت',
    version: 'نسخه',
    executor: 'اجراکننده',
    startTime: 'زمان شروع',
    time: 'زمان گذشته',
    tokens: 'کل توکن‌ها',
    steps: 'گام‌های اجرا',
  },
  resultEmpty: {
    title: 'این اجرا فقط خروجی به فرمت JSON دارد،',
    tipLeft: 'لطفاً به ',
    link: 'پنل جزئیات',
    tipRight: ' بروید و آن را مشاهده کنید.',
  },
  actionLogs: 'گزارش های اکشن',
  circularInvocationTip: 'فراخوانی دایره ای ابزارها/گره ها در گردش کار فعلی وجود دارد.',
}

export default translation
